import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:culture_connect/models/messaging/file_message_model.dart';
import 'package:culture_connect/providers/messaging/file_sharing_provider.dart';

/// Widget for picking and uploading files
class FilePickerWidget extends ConsumerStatefulWidget {
  final String messageId;
  final Function(FileMessageModel) onFileUploaded;
  final FilePermissions? defaultPermissions;
  final bool allowMultiple;
  final List<FileType>? allowedTypes;

  const FilePickerWidget({
    super.key,
    required this.messageId,
    required this.onFileUploaded,
    this.defaultPermissions,
    this.allowMultiple = false,
    this.allowedTypes,
  });

  @override
  ConsumerState<FilePickerWidget> createState() => _FilePickerWidgetState();
}

class _FilePickerWidgetState extends ConsumerState<FilePickerWidget> {
  final ImagePicker _imagePicker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final uploadState = ref.watch(fileUploadProvider);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.attach_file,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Attach File',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
                iconSize: 20,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Upload progress
          if (uploadState.isUploading) ...[
            LinearProgressIndicator(value: uploadState.progress),
            const SizedBox(height: 8),
            Text(
              'Uploading... ${(uploadState.progress * 100).toInt()}%',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // Error message
          if (uploadState.error != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: theme.colorScheme.onErrorContainer,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      uploadState.error!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onErrorContainer,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // File type options
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.2,
            children: [
              if (_isTypeAllowed(FileType.image))
                _buildFileTypeOption(
                  context,
                  icon: Icons.image,
                  label: 'Photos',
                  color: Colors.green,
                  onTap: () => _pickImage(ImageSource.gallery),
                ),
              
              if (_isTypeAllowed(FileType.image))
                _buildFileTypeOption(
                  context,
                  icon: Icons.camera_alt,
                  label: 'Camera',
                  color: Colors.blue,
                  onTap: () => _pickImage(ImageSource.camera),
                ),
              
              if (_isTypeAllowed(FileType.video))
                _buildFileTypeOption(
                  context,
                  icon: Icons.videocam,
                  label: 'Video',
                  color: Colors.red,
                  onTap: () => _pickVideo(),
                ),
              
              if (_isTypeAllowed(FileType.audio))
                _buildFileTypeOption(
                  context,
                  icon: Icons.audiotrack,
                  label: 'Audio',
                  color: Colors.orange,
                  onTap: () => _pickAudio(),
                ),
              
              if (_isTypeAllowed(FileType.document))
                _buildFileTypeOption(
                  context,
                  icon: Icons.description,
                  label: 'Document',
                  color: Colors.purple,
                  onTap: () => _pickDocument(),
                ),
              
              _buildFileTypeOption(
                context,
                icon: Icons.folder,
                label: 'Browse',
                color: theme.colorScheme.primary,
                onTap: () => _pickAnyFile(),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // File permissions section
          if (!uploadState.isUploading) ...[
            ExpansionTile(
              title: Text(
                'File Permissions',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              leading: const Icon(Icons.security),
              children: [
                _buildPermissionsEditor(),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFileTypeOption(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: color.withAlpha(26),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withAlpha(77),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsEditor() {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('Allow Download'),
            subtitle: const Text('Users can download this file'),
            value: _permissions.canDownload,
            onChanged: (value) {
              setState(() {
                _permissions = _permissions.copyWith(canDownload: value);
              });
            },
          ),
          
          SwitchListTile(
            title: const Text('Allow Sharing'),
            subtitle: const Text('Users can share this file'),
            value: _permissions.canShare,
            onChanged: (value) {
              setState(() {
                _permissions = _permissions.copyWith(canShare: value);
              });
            },
          ),
          
          SwitchListTile(
            title: const Text('Allow Preview'),
            subtitle: const Text('Users can preview this file'),
            value: _permissions.canPreview,
            onChanged: (value) {
              setState(() {
                _permissions = _permissions.copyWith(canPreview: value);
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          // Expiration date
          ListTile(
            leading: const Icon(Icons.schedule),
            title: const Text('Expiration Date'),
            subtitle: Text(
              _permissions.expiresAt != null
                  ? 'Expires on ${_formatDate(_permissions.expiresAt!)}'
                  : 'No expiration',
            ),
            trailing: IconButton(
              onPressed: () => _selectExpirationDate(),
              icon: const Icon(Icons.calendar_today),
            ),
          ),
        ],
      ),
    );
  }

  FilePermissions _permissions = const FilePermissions();

  @override
  void initState() {
    super.initState();
    _permissions = widget.defaultPermissions ?? const FilePermissions();
  }

  bool _isTypeAllowed(FileType type) {
    if (widget.allowedTypes == null) return true;
    return widget.allowedTypes!.contains(type);
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final pickedFile = await _imagePicker.pickImage(source: source);
      if (pickedFile != null) {
        await _uploadFile(File(pickedFile.path));
      }
    } catch (e) {
      _showError('Failed to pick image: $e');
    }
  }

  Future<void> _pickVideo() async {
    try {
      final pickedFile = await _imagePicker.pickVideo(source: ImageSource.gallery);
      if (pickedFile != null) {
        await _uploadFile(File(pickedFile.path));
      }
    } catch (e) {
      _showError('Failed to pick video: $e');
    }
  }

  Future<void> _pickAudio() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: widget.allowMultiple,
      );
      
      if (result != null && result.files.isNotEmpty) {
        for (final file in result.files) {
          if (file.path != null) {
            await _uploadFile(File(file.path!));
          }
        }
      }
    } catch (e) {
      _showError('Failed to pick audio: $e');
    }
  }

  Future<void> _pickDocument() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
        allowMultiple: widget.allowMultiple,
      );
      
      if (result != null && result.files.isNotEmpty) {
        for (final file in result.files) {
          if (file.path != null) {
            await _uploadFile(File(file.path!));
          }
        }
      }
    } catch (e) {
      _showError('Failed to pick document: $e');
    }
  }

  Future<void> _pickAnyFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: widget.allowMultiple,
      );
      
      if (result != null && result.files.isNotEmpty) {
        for (final file in result.files) {
          if (file.path != null) {
            await _uploadFile(File(file.path!));
          }
        }
      }
    } catch (e) {
      _showError('Failed to pick file: $e');
    }
  }

  Future<void> _uploadFile(File file) async {
    final uploadedFile = await ref.read(fileUploadProvider.notifier).uploadFile(
      file: file,
      messageId: widget.messageId,
      permissions: _permissions,
    );

    if (uploadedFile != null) {
      widget.onFileUploaded(uploadedFile);
      if (mounted) {
        Navigator.pop(context);
      }
    }
  }

  Future<void> _selectExpirationDate() async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: _permissions.expiresAt ?? DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (selectedDate != null) {
      setState(() {
        _permissions = _permissions.copyWith(expiresAt: selectedDate);
      });
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
