import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:open_file/open_file.dart';
import 'package:share_plus/share_plus.dart';
import 'package:culture_connect/models/messaging/file_message_model.dart';
import 'package:culture_connect/providers/messaging/file_sharing_provider.dart';
import 'package:culture_connect/widgets/messaging/file_preview_widget.dart';

/// Widget for managing file downloads and actions
class FileDownloadManager extends ConsumerWidget {
  final FileMessageModel fileMessage;
  final bool showPreview;
  final VoidCallback? onClose;

  const FileDownloadManager({
    super.key,
    required this.fileMessage,
    this.showPreview = true,
    this.onClose,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final downloadState = ref.watch(fileDownloadProvider);
    final managementState = ref.watch(fileManagementProvider);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.file_download,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  fileMessage.fileName,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (onClose != null)
                IconButton(
                  onPressed: onClose,
                  icon: const Icon(Icons.close),
                  iconSize: 20,
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // File preview
          if (showPreview && fileMessage.permissions.canPreview)
            Container(
              height: 200,
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 16),
              child: FilePreviewWidget(
                fileMessage: fileMessage,
                onTap: () => _openFile(context, ref),
              ),
            ),
          
          // File information
          _buildFileInfo(context, theme),
          
          const SizedBox(height: 16),
          
          // Download progress
          if (downloadState.isFileDownloading(fileMessage.id))
            _buildDownloadProgress(context, theme, downloadState),
          
          // Error message
          if (downloadState.getDownloadError(fileMessage.id) != null)
            _buildErrorMessage(context, theme, downloadState),
          
          // Management error
          if (managementState.error != null)
            _buildManagementError(context, theme, managementState),
          
          // Success message
          if (managementState.successMessage != null)
            _buildSuccessMessage(context, theme, managementState),
          
          const SizedBox(height: 16),
          
          // Action buttons
          _buildActionButtons(context, ref, downloadState, managementState),
        ],
      ),
    );
  }

  Widget _buildFileInfo(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: theme.colorScheme.onSurfaceVariant,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'File Information',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Size:',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                fileMessage.formattedFileSize,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Type:',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  fileMessage.fileExtension.toUpperCase(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Status:',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                fileMessage.isLocallyAvailable ? 'Downloaded' : 'Online',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: fileMessage.isLocallyAvailable 
                      ? Colors.green 
                      : theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          
          if (fileMessage.permissions.expiresAt != null) ...[
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Expires:',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  _formatDate(fileMessage.permissions.expiresAt!),
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: fileMessage.permissions.isExpired 
                        ? theme.colorScheme.error 
                        : theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDownloadProgress(BuildContext context, ThemeData theme, FileDownloadState downloadState) {
    final progress = downloadState.getDownloadProgress(fileMessage.id);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  value: progress,
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.onPrimaryContainer,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Downloading... ${(progress * 100).toInt()}%',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: theme.colorScheme.onPrimaryContainer.withAlpha(77),
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorMessage(BuildContext context, ThemeData theme, FileDownloadState downloadState) {
    final error = downloadState.getDownloadError(fileMessage.id);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: theme.colorScheme.onErrorContainer,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onErrorContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManagementError(BuildContext context, ThemeData theme, FileManagementState managementState) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: theme.colorScheme.onErrorContainer,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              managementState.error!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onErrorContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage(BuildContext context, ThemeData theme, FileManagementState managementState) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.check_circle_outline,
            color: Colors.green,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              managementState.successMessage!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.green,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    WidgetRef ref,
    FileDownloadState downloadState,
    FileManagementState managementState,
  ) {
    final theme = Theme.of(context);
    final isDownloading = downloadState.isFileDownloading(fileMessage.id);
    final hasError = downloadState.getDownloadError(fileMessage.id) != null;
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        // Download/Open button
        if (fileMessage.permissions.canDownload)
          ElevatedButton.icon(
            onPressed: isDownloading || managementState.isDeleting
                ? null
                : () => _handleDownload(context, ref),
            icon: Icon(
              fileMessage.isLocallyAvailable 
                  ? Icons.folder_open 
                  : hasError 
                      ? Icons.refresh 
                      : Icons.download,
            ),
            label: Text(
              fileMessage.isLocallyAvailable 
                  ? 'Open' 
                  : hasError 
                      ? 'Retry' 
                      : 'Download',
            ),
          ),
        
        // Share button
        if (fileMessage.permissions.canShare && fileMessage.isLocallyAvailable)
          OutlinedButton.icon(
            onPressed: managementState.isDeleting ? null : () => _shareFile(context),
            icon: const Icon(Icons.share),
            label: const Text('Share'),
          ),
        
        // Delete button
        if (fileMessage.permissions.canDelete)
          OutlinedButton.icon(
            onPressed: isDownloading || managementState.isDeleting
                ? null
                : () => _deleteFile(context, ref),
            icon: managementState.isDeleting
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Icon(
                    Icons.delete,
                    color: theme.colorScheme.error,
                  ),
            label: Text(
              'Delete',
              style: TextStyle(color: theme.colorScheme.error),
            ),
          ),
      ],
    );
  }

  Future<void> _handleDownload(BuildContext context, WidgetRef ref) async {
    if (fileMessage.isLocallyAvailable) {
      await _openFile(context, ref);
    } else {
      await ref.read(fileDownloadProvider.notifier).downloadFile(fileMessage);
    }
  }

  Future<void> _openFile(BuildContext context, WidgetRef ref) async {
    if (fileMessage.isLocallyAvailable) {
      try {
        await OpenFile.open(fileMessage.localPath!);
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to open file: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  Future<void> _shareFile(BuildContext context) async {
    if (fileMessage.isLocallyAvailable) {
      try {
        await Share.shareXFiles([XFile(fileMessage.localPath!)]);
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to share file: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteFile(BuildContext context, WidgetRef ref) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete File'),
        content: Text('Are you sure you want to delete "${fileMessage.fileName}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await ref.read(fileManagementProvider.notifier).deleteFile(fileMessage.id);
      if (success && context.mounted) {
        Navigator.pop(context);
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
