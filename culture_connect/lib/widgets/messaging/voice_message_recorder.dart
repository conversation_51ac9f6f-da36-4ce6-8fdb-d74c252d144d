import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/providers/messaging/voice_message_provider.dart';
import 'package:culture_connect/models/messaging/voice_message_model.dart';
import 'package:culture_connect/widgets/messaging/waveform_visualizer.dart';

/// Widget for recording voice messages
class VoiceMessageRecorder extends ConsumerStatefulWidget {
  final Function(VoiceMessageModel) onVoiceMessageRecorded;
  final VoiceRecordingConfig config;
  final bool showWaveform;
  final Color? primaryColor;
  final Color? backgroundColor;

  const VoiceMessageRecorder({
    super.key,
    required this.onVoiceMessageRecorded,
    this.config = const VoiceRecordingConfig(),
    this.showWaveform = true,
    this.primaryColor,
    this.backgroundColor,
  });

  @override
  ConsumerState<VoiceMessageRecorder> createState() => _VoiceMessageRecorderState();
}

class _VoiceMessageRecorderState extends ConsumerState<VoiceMessageRecorder>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final recordingState = ref.watch(voiceRecordingStateProvider);
    final recordingNotifier = ref.read(voiceRecordingStateProvider.notifier);
    
    // Listen to recording duration
    ref.listen(recordingDurationProvider, (previous, next) {
      next.whenData((duration) {
        recordingNotifier.updateDuration(duration);
      });
    });

    // Listen to amplitude data
    ref.listen(amplitudeProvider, (previous, next) {
      next.whenData((amplitude) {
        recordingNotifier.updateWaveform(amplitude);
      });
    });

    // Handle recording state changes
    ref.listen(voiceRecordingStateProvider, (previous, current) {
      if (current.isRecording && !_pulseController.isAnimating) {
        _pulseController.repeat(reverse: true);
      } else if (!current.isRecording && _pulseController.isAnimating) {
        _pulseController.stop();
        _pulseController.reset();
      }
    });

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withAlpha(26),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.mic,
                color: widget.primaryColor ?? theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                recordingState.isRecording ? 'Recording...' : 'Voice Message',
                style: theme.textTheme.titleSmall?.copyWith(
                  color: widget.primaryColor ?? theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (recordingState.isRecording)
                Text(
                  _formatDuration(recordingState.duration),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Waveform visualization
          if (widget.showWaveform && recordingState.isRecording)
            Container(
              height: 60,
              margin: const EdgeInsets.symmetric(vertical: 8),
              child: WaveformVisualizer(
                waveform: recordingState.waveform,
                color: widget.primaryColor ?? theme.colorScheme.primary,
                isAnimating: recordingState.isRecording,
              ),
            ),
          
          // Recording controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Cancel button (only show when recording)
              if (recordingState.isRecording)
                _buildControlButton(
                  icon: Icons.close,
                  label: 'Cancel',
                  color: theme.colorScheme.error,
                  onPressed: () => _cancelRecording(recordingNotifier),
                ),
              
              // Record/Stop button
              GestureDetector(
                onTapDown: (_) => _scaleController.forward(),
                onTapUp: (_) => _scaleController.reverse(),
                onTapCancel: () => _scaleController.reverse(),
                child: AnimatedBuilder(
                  animation: Listenable.merge([_pulseAnimation, _scaleAnimation]),
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _scaleAnimation.value,
                      child: Transform.scale(
                        scale: recordingState.isRecording ? _pulseAnimation.value : 1.0,
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: recordingState.isRecording
                                ? theme.colorScheme.error
                                : (widget.primaryColor ?? theme.colorScheme.primary),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: (recordingState.isRecording
                                        ? theme.colorScheme.error
                                        : (widget.primaryColor ?? theme.colorScheme.primary))
                                    .withAlpha(77),
                                blurRadius: 12,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(40),
                              onTap: recordingState.isRecording
                                  ? () => _stopRecording(recordingNotifier)
                                  : () => _startRecording(recordingNotifier),
                              child: Icon(
                                recordingState.isRecording ? Icons.stop : Icons.mic,
                                color: Colors.white,
                                size: 32,
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              // Send button (only show when recording)
              if (recordingState.isRecording)
                _buildControlButton(
                  icon: Icons.send,
                  label: 'Send',
                  color: theme.colorScheme.primary,
                  onPressed: () => _sendRecording(recordingNotifier),
                ),
            ],
          ),
          
          // Error message
          if (recordingState.error != null)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                recordingState.error!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.error,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          
          // Recording tips
          if (!recordingState.isRecording)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'Tap and hold to record, release to send',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: color.withAlpha(26),
            shape: BoxShape.circle,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(24),
              onTap: onPressed,
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Future<void> _startRecording(VoiceRecordingNotifier notifier) async {
    HapticFeedback.lightImpact();
    await notifier.startRecording(config: widget.config);
  }

  Future<void> _stopRecording(VoiceRecordingNotifier notifier) async {
    HapticFeedback.mediumImpact();
    final path = await notifier.stopRecording();
    if (path != null) {
      await _processRecording(path);
    }
  }

  Future<void> _cancelRecording(VoiceRecordingNotifier notifier) async {
    HapticFeedback.lightImpact();
    await notifier.stopRecording();
    notifier.reset();
  }

  Future<void> _sendRecording(VoiceRecordingNotifier notifier) async {
    HapticFeedback.mediumImpact();
    final path = await notifier.stopRecording();
    if (path != null) {
      await _processRecording(path);
    }
  }

  Future<void> _processRecording(String filePath) async {
    try {
      // Create voice message model
      final voiceMessage = await ref.read(voiceMessageCreationProvider(
        VoiceMessageCreationParams(
          messageId: DateTime.now().millisecondsSinceEpoch.toString(),
          filePath: filePath,
        ),
      ).future);

      if (voiceMessage != null) {
        widget.onVoiceMessageRecorded(voiceMessage);
        ref.read(voiceRecordingStateProvider.notifier).reset();
      }
    } catch (e) {
      ref.read(voiceRecordingStateProvider.notifier).state = 
          ref.read(voiceRecordingStateProvider.notifier).state.copyWith(
            error: 'Failed to process recording: $e',
          );
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
