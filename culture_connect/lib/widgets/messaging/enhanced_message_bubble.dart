import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/providers/chat_provider.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/messaging/message_reactions.dart';
import 'package:culture_connect/widgets/messaging/custom_emoji_picker.dart';

/// Enhanced message bubble with integrated reaction system
class EnhancedMessageBubble extends ConsumerStatefulWidget {
  final MessageModel message;
  final bool isMe;
  final bool showAvatar;
  final bool showTimestamp;

  const EnhancedMessageBubble({
    super.key,
    required this.message,
    required this.isMe,
    this.showAvatar = true,
    this.showTimestamp = true,
  });

  @override
  ConsumerState<EnhancedMessageBubble> createState() => _EnhancedMessageBubbleState();
}

class _EnhancedMessageBubbleState extends ConsumerState<EnhancedMessageBubble>
    with TickerProviderStateMixin {
  bool _showReactionSelector = false;
  bool _showEmojiPicker = false;
  late AnimationController _reactionSelectorController;
  late Animation<double> _reactionSelectorAnimation;

  @override
  void initState() {
    super.initState();
    _reactionSelectorController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _reactionSelectorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _reactionSelectorController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _reactionSelectorController.dispose();
    super.dispose();
  }

  void _showReactionSelectorOverlay() {
    setState(() {
      _showReactionSelector = true;
    });
    _reactionSelectorController.forward();
  }

  void _hideReactionSelector() {
    _reactionSelectorController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _showReactionSelector = false;
        });
      }
    });
  }

  void _showEmojiPickerModal() {
    HapticFeedback.mediumImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => CustomEmojiPicker(
        message: widget.message,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  Future<void> _addReaction(ReactionType reactionType) async {
    try {
      await ref.read(chatProvider.notifier).addReaction(
        widget.message,
        reactionType,
      );
      _hideReactionSelector();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add reaction: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _removeReaction() async {
    try {
      await ref.read(chatProvider.notifier).removeReaction(widget.message);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to remove reaction: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserModelProvider).value;
    
    return GestureDetector(
      onLongPress: () {
        HapticFeedback.mediumImpact();
        _showReactionSelectorOverlay();
      },
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
        child: Column(
          crossAxisAlignment: widget.isMe 
              ? CrossAxisAlignment.end 
              : CrossAxisAlignment.start,
          children: [
            // Message bubble
            Row(
              mainAxisAlignment: widget.isMe 
                  ? MainAxisAlignment.end 
                  : MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (!widget.isMe && widget.showAvatar) ...[
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: AppTheme.primaryColor.withAlpha(51),
                    child: Text(
                      widget.message.senderName.isNotEmpty
                          ? widget.message.senderName[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                
                Flexible(
                  child: Stack(
                    children: [
                      // Message content
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          color: widget.isMe 
                              ? AppTheme.primaryColor 
                              : Colors.grey[100],
                          borderRadius: BorderRadius.circular(20).copyWith(
                            bottomRight: widget.isMe 
                                ? const Radius.circular(4) 
                                : const Radius.circular(20),
                            bottomLeft: !widget.isMe 
                                ? const Radius.circular(4) 
                                : const Radius.circular(20),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(13),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (!widget.isMe) ...[
                              Text(
                                widget.message.senderName,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: AppTheme.primaryColor,
                                ),
                              ),
                              const SizedBox(height: 4),
                            ],
                            Text(
                              widget.message.content,
                              style: TextStyle(
                                fontSize: 16,
                                color: widget.isMe 
                                    ? Colors.white 
                                    : AppTheme.textPrimaryColor,
                              ),
                            ),
                            if (widget.showTimestamp) ...[
                              const SizedBox(height: 4),
                              Text(
                                _formatTimestamp(widget.message.timestamp),
                                style: TextStyle(
                                  fontSize: 11,
                                  color: widget.isMe 
                                      ? Colors.white.withAlpha(179) 
                                      : AppTheme.textSecondaryColor,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      
                      // Reaction selector overlay
                      if (_showReactionSelector)
                        Positioned(
                          top: -60,
                          left: widget.isMe ? null : 0,
                          right: widget.isMe ? 0 : null,
                          child: AnimatedBuilder(
                            animation: _reactionSelectorAnimation,
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _reactionSelectorAnimation.value,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    MessageReactionSelector(
                                      message: widget.message,
                                      onClose: _hideReactionSelector,
                                    ),
                                    const SizedBox(width: 8),
                                    GestureDetector(
                                      onTap: _showEmojiPickerModal,
                                      child: Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          shape: BoxShape.circle,
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withAlpha(51),
                                              blurRadius: 8,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: const Icon(
                                          Icons.add,
                                          size: 20,
                                          color: AppTheme.primaryColor,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                    ],
                  ),
                ),
                
                if (widget.isMe && widget.showAvatar) ...[
                  const SizedBox(width: 8),
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: AppTheme.primaryColor.withAlpha(51),
                    child: Text(
                      currentUser?.fullName.isNotEmpty == true
                          ? currentUser!.fullName[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ],
            ),
            
            // Reactions
            MessageReactionsWidget(
              message: widget.message,
              isMe: widget.isMe,
              onReactionAdded: _addReaction,
              onReactionRemoved: _removeReaction,
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${timestamp.day}/${timestamp.month}';
    }
  }
}
