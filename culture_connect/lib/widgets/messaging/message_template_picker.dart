import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/messaging/message_template_model.dart';
import 'package:culture_connect/providers/messaging/advanced_messaging_provider.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// Widget for picking message templates and quick replies
class MessageTemplatePicker extends ConsumerStatefulWidget {
  final Function(String) onTemplateSelected;
  final String? contextMessage;

  const MessageTemplatePicker({
    super.key,
    required this.onTemplateSelected,
    this.contextMessage,
  });

  @override
  ConsumerState<MessageTemplatePicker> createState() =>
      _MessageTemplatePickerState();
}

class _MessageTemplatePickerState extends ConsumerState<MessageTemplatePicker>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  MessageTemplateCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadData() {
    final currentUser = ref.read(authStateProvider).user;
    if (currentUser == null) return;

    final templateNotifier = ref.read(messageTemplateProvider.notifier);
    templateNotifier.loadUserTemplates(currentUser.id);
    templateNotifier.loadContextualQuickReplies(widget.contextMessage ?? '');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withAlpha(77),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Text(
                  'Templates & Quick Replies',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Quick Replies'),
              Tab(text: 'Templates'),
            ],
          ),

          // Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildQuickRepliesTab(),
                _buildTemplatesTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickRepliesTab() {
    final templateState = ref.watch(messageTemplateProvider);
    final quickReplies = templateState.quickReplies;

    if (quickReplies.isEmpty) {
      return _buildEmptyState('No quick replies available');
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: quickReplies.length,
      itemBuilder: (context, index) {
        final quickReply = quickReplies[index];
        return _buildQuickReplyTile(quickReply);
      },
    );
  }

  Widget _buildTemplatesTab() {
    final templateState = ref.watch(messageTemplateProvider);
    final templates = templateState.templates;

    return Column(
      children: [
        // Search and filter
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Search bar
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search templates...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            _searchTemplates();
                          },
                          icon: const Icon(Icons.clear),
                        )
                      : null,
                ),
                onChanged: (_) => _searchTemplates(),
              ),

              const SizedBox(height: 12),

              // Category filter
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildCategoryChip(null, 'All'),
                    const SizedBox(width: 8),
                    ...MessageTemplateCategory.values.map(
                      (category) => Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child:
                            _buildCategoryChip(category, category.displayName),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Templates list
        Expanded(
          child: templates.isEmpty
              ? _buildEmptyState('No templates found')
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: templates.length,
                  itemBuilder: (context, index) {
                    final template = templates[index];
                    return _buildTemplateTile(template);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildQuickReplyTile(QuickReplyModel quickReply) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Text(
              quickReply.category.emoji,
              style: const TextStyle(fontSize: 20),
            ),
          ),
        ),
        title: Text(
          quickReply.displayText,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          quickReply.category.displayName,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withAlpha(153),
          ),
        ),
        onTap: () {
          widget.onTemplateSelected(quickReply.text);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  Widget _buildTemplateTile(MessageTemplateModel template) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: theme.colorScheme.secondaryContainer,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Text(
              template.category.emoji,
              style: const TextStyle(fontSize: 20),
            ),
          ),
        ),
        title: Text(
          template.name,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              template.content,
              style: theme.textTheme.bodySmall,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  template.category.displayName,
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Used ${template.usageCount} times',
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(128),
                  ),
                ),
              ],
            ),
          ],
        ),
        isThreeLine: true,
        trailing: template.hasVariables
            ? Icon(
                Icons.code,
                size: 16,
                color: theme.colorScheme.onSurface.withAlpha(128),
              )
            : null,
        onTap: () => _useTemplate(template),
      ),
    );
  }

  Widget _buildCategoryChip(MessageTemplateCategory? category, String label) {
    final theme = Theme.of(context);
    final isSelected = _selectedCategory == category;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedCategory = selected ? category : null;
        });
        _searchTemplates();
      },
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      selectedColor: theme.colorScheme.primaryContainer,
    );
  }

  Widget _buildEmptyState(String message) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 48,
            color: theme.colorScheme.onSurface.withAlpha(128),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(153),
            ),
          ),
        ],
      ),
    );
  }

  void _searchTemplates() {
    final currentUser = ref.read(authStateProvider).user;
    if (currentUser == null) return;

    final templateNotifier = ref.read(messageTemplateProvider.notifier);
    final query = _searchController.text.trim();

    if (query.isEmpty && _selectedCategory == null) {
      templateNotifier.loadUserTemplates(currentUser.id);
    } else {
      templateNotifier.searchTemplates(
        userId: currentUser.id,
        query: query.isEmpty ? '' : query,
        category: _selectedCategory,
      );
    }
  }

  void _useTemplate(MessageTemplateModel template) {
    final templateNotifier = ref.read(messageTemplateProvider.notifier);
    templateNotifier.useTemplate(template.id);

    if (template.hasVariables) {
      // Show variable input dialog
      _showVariableInputDialog(template);
    } else {
      widget.onTemplateSelected(template.content);
      Navigator.of(context).pop();
    }
  }

  void _showVariableInputDialog(MessageTemplateModel template) {
    final variables = <String, String>{};

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fill Template Variables'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: template.templateVariables.map((variable) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: TextField(
                decoration: InputDecoration(
                  labelText: variable,
                  border: const OutlineInputBorder(),
                ),
                onChanged: (value) {
                  variables[variable] = value;
                },
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              final processedContent = template.processContent(variables);
              widget.onTemplateSelected(processedContent);
              Navigator.of(context).pop(); // Close variable dialog
              Navigator.of(context).pop(); // Close template picker
            },
            child: const Text('Use Template'),
          ),
        ],
      ),
    );
  }

  /// Show template picker
  static void show(
    BuildContext context, {
    required Function(String) onTemplateSelected,
    String? contextMessage,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MessageTemplatePicker(
        onTemplateSelected: onTemplateSelected,
        contextMessage: contextMessage,
      ),
    );
  }
}
