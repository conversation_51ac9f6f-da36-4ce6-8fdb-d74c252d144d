import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:video_player/video_player.dart';
import 'package:culture_connect/models/messaging/video_message_model.dart';
import 'package:culture_connect/providers/messaging/video_message_provider.dart';

/// Widget for playing video messages
class VideoMessagePlayer extends ConsumerStatefulWidget {
  final VideoMessageModel videoMessage;
  final Color? primaryColor;
  final Color? backgroundColor;
  final bool showControls;
  final bool autoPlay;

  const VideoMessagePlayer({
    super.key,
    required this.videoMessage,
    this.primaryColor,
    this.backgroundColor,
    this.showControls = true,
    this.autoPlay = false,
  });

  @override
  ConsumerState<VideoMessagePlayer> createState() => _VideoMessagePlayerState();
}

class _VideoMessagePlayerState extends ConsumerState<VideoMessagePlayer>
    with TickerProviderStateMixin {
  late AnimationController _controlsController;
  late AnimationController _playButtonController;
  late Animation<double> _controlsAnimation;
  late Animation<double> _playButtonAnimation;
  
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    
    if (widget.autoPlay) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _playVideo();
      });
    }
  }

  void _initializeAnimations() {
    _controlsController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _playButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _controlsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsController,
      curve: Curves.easeInOut,
    ));

    _playButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _playButtonController,
      curve: Curves.easeInOut,
    ));

    _controlsController.forward();
  }

  @override
  void dispose() {
    _controlsController.dispose();
    _playButtonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final playbackState = ref.watch(videoPlaybackStateProvider);
    final playbackNotifier = ref.read(videoPlaybackNotifierProvider.notifier);
    
    final isCurrentMessage = playbackState.whenData((state) => 
        state.currentMessageId == widget.videoMessage.id).value ?? false;
    final isPlaying = playbackState.whenData((state) => 
        state.isPlaying && isCurrentMessage).value ?? false;
    final isLoading = playbackState.whenData((state) => 
        state.isLoading && isCurrentMessage).value ?? false;
    final position = playbackState.whenData((state) => 
        isCurrentMessage ? state.position : Duration.zero).value ?? Duration.zero;
    final volume = playbackState.whenData((state) => 
        state.volume).value ?? 1.0;

    final progress = widget.videoMessage.duration.inMilliseconds > 0
        ? position.inMilliseconds / widget.videoMessage.duration.inMilliseconds
        : 0.0;

    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withAlpha(26),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Video player area
          Container(
            height: 200,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
            ),
            child: ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              child: Stack(
                children: [
                  // Video thumbnail or player
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: theme.colorScheme.surfaceContainerHigh,
                    child: widget.videoMessage.thumbnailUrl != null
                        ? Image.network(
                            widget.videoMessage.thumbnailUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => _buildVideoPlaceholder(theme),
                          )
                        : _buildVideoPlaceholder(theme),
                  ),
                  
                  // Play overlay
                  if (!isPlaying)
                    Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: Colors.black.withAlpha(77),
                      child: Center(
                        child: GestureDetector(
                          onTapDown: (_) => _playButtonController.forward(),
                          onTapUp: (_) => _playButtonController.reverse(),
                          onTapCancel: () => _playButtonController.reverse(),
                          child: AnimatedBuilder(
                            animation: _playButtonAnimation,
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _playButtonAnimation.value,
                                child: Container(
                                  width: 64,
                                  height: 64,
                                  decoration: BoxDecoration(
                                    color: widget.primaryColor ?? theme.colorScheme.primary,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: (widget.primaryColor ?? theme.colorScheme.primary)
                                            .withAlpha(77),
                                        blurRadius: 12,
                                        spreadRadius: 2,
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(32),
                                      onTap: _playVideo,
                                      child: isLoading
                                          ? const SizedBox(
                                              width: 24,
                                              height: 24,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                              ),
                                            )
                                          : const Icon(
                                              Icons.play_arrow,
                                              color: Colors.white,
                                              size: 32,
                                            ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  
                  // Video info overlay
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(128),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _formatDuration(widget.videoMessage.duration),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  
                  // Resolution info
                  Positioned(
                    top: 12,
                    left: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(128),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${widget.videoMessage.resolution.width}x${widget.videoMessage.resolution.height}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Controls
          if (widget.showControls)
            AnimatedBuilder(
              animation: _controlsAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _controlsAnimation.value,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        // Progress bar
                        Row(
                          children: [
                            Text(
                              _formatDuration(position),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: SliderTheme(
                                data: SliderTheme.of(context).copyWith(
                                  trackHeight: 4,
                                  thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                                  overlayShape: const RoundSliderOverlayShape(overlayRadius: 12),
                                ),
                                child: Slider(
                                  value: progress.clamp(0.0, 1.0),
                                  onChanged: (value) => _seekToPosition(value, playbackNotifier),
                                  activeColor: widget.primaryColor ?? theme.colorScheme.primary,
                                  inactiveColor: theme.colorScheme.surfaceContainerHigh,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _formatDuration(widget.videoMessage.duration),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // Control buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            // Play/Pause button
                            IconButton(
                              onPressed: () => _togglePlayback(playbackNotifier, isPlaying),
                              icon: Icon(
                                isPlaying ? Icons.pause : Icons.play_arrow,
                                color: widget.primaryColor ?? theme.colorScheme.primary,
                              ),
                            ),
                            
                            // Volume control
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  volume > 0.5 ? Icons.volume_up : 
                                  volume > 0 ? Icons.volume_down : Icons.volume_off,
                                  size: 20,
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                                const SizedBox(width: 4),
                                SizedBox(
                                  width: 80,
                                  child: SliderTheme(
                                    data: SliderTheme.of(context).copyWith(
                                      trackHeight: 2,
                                      thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 4),
                                      overlayShape: const RoundSliderOverlayShape(overlayRadius: 8),
                                    ),
                                    child: Slider(
                                      value: volume,
                                      onChanged: (value) => playbackNotifier.setVolume(value),
                                      activeColor: widget.primaryColor ?? theme.colorScheme.primary,
                                      inactiveColor: theme.colorScheme.surfaceContainerHigh,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            
                            // Fullscreen button
                            IconButton(
                              onPressed: () => _openFullscreen(context),
                              icon: Icon(
                                Icons.fullscreen,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildVideoPlaceholder(ThemeData theme) {
    return Container(
      color: theme.colorScheme.surfaceContainerHigh,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.videocam,
              size: 48,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 8),
            Text(
              'Video Message',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _playVideo() async {
    HapticFeedback.lightImpact();
    final playbackNotifier = ref.read(videoPlaybackNotifierProvider.notifier);
    await playbackNotifier.playVideoMessage(widget.videoMessage);
  }

  Future<void> _togglePlayback(VideoPlaybackNotifier notifier, bool isPlaying) async {
    HapticFeedback.lightImpact();
    
    if (isPlaying) {
      await notifier.pauseVideoMessage();
    } else {
      await notifier.playVideoMessage(widget.videoMessage);
    }
  }

  void _seekToPosition(double progress, VideoPlaybackNotifier notifier) {
    final position = Duration(
      milliseconds: (widget.videoMessage.duration.inMilliseconds * progress).round(),
    );
    
    HapticFeedback.selectionClick();
    notifier.seekToPosition(position);
  }

  void _openFullscreen(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VideoMessageFullscreen(
          videoMessage: widget.videoMessage,
          primaryColor: widget.primaryColor,
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

/// Fullscreen video player
class VideoMessageFullscreen extends ConsumerWidget {
  final VideoMessageModel videoMessage;
  final Color? primaryColor;

  const VideoMessageFullscreen({
    super.key,
    required this.videoMessage,
    this.primaryColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Video player
            Center(
              child: VideoMessagePlayer(
                videoMessage: videoMessage,
                primaryColor: primaryColor,
                backgroundColor: Colors.transparent,
                autoPlay: true,
              ),
            ),
            
            // Close button
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha(128),
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
