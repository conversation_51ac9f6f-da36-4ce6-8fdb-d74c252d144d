import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/providers/messaging/advanced_messaging_provider.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// Dialog for scheduling messages
class MessageScheduleDialog extends ConsumerStatefulWidget {
  final String chatId;
  final String recipientId;
  final String text;
  final MessageType type;
  final String? mediaUrl;

  const MessageScheduleDialog({
    super.key,
    required this.chatId,
    required this.recipientId,
    required this.text,
    required this.type,
    this.mediaUrl,
  });

  @override
  ConsumerState<MessageScheduleDialog> createState() =>
      _MessageScheduleDialogState();
}

class _MessageScheduleDialogState extends ConsumerState<MessageScheduleDialog> {
  DateTime _selectedDate = DateTime.now().add(const Duration(hours: 1));
  TimeOfDay _selectedTime = TimeOfDay.now();
  bool _isScheduling = false;

  @override
  void initState() {
    super.initState();
    // Set default time to next hour
    final now = DateTime.now();
    _selectedTime = TimeOfDay(hour: (now.hour + 1) % 24, minute: 0);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final scheduledDateTime = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
      _selectedTime.hour,
      _selectedTime.minute,
    );

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.schedule,
            color: theme.colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 8),
          const Text('Schedule Message'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Message preview
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Message Preview:',
                  style: theme.textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.text,
                  style: theme.textTheme.bodyMedium,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Date selection
          Text(
            'Select Date',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),

          InkWell(
            onTap: _selectDate,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(77),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 20,
                    color: theme.colorScheme.onSurface.withAlpha(153),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    _formatDate(_selectedDate),
                    style: theme.textTheme.bodyMedium,
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_drop_down,
                    color: theme.colorScheme.onSurface.withAlpha(153),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Time selection
          Text(
            'Select Time',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),

          InkWell(
            onTap: _selectTime,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(77),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 20,
                    color: theme.colorScheme.onSurface.withAlpha(153),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    _selectedTime.format(context),
                    style: theme.textTheme.bodyMedium,
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_drop_down,
                    color: theme.colorScheme.onSurface.withAlpha(153),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Scheduled time info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withAlpha(77),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Message will be sent on ${_formatDateTime(scheduledDateTime)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Quick time options
          const SizedBox(height: 16),
          Text(
            'Quick Options',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),

          Wrap(
            spacing: 8,
            children: [
              _buildQuickTimeChip(context, '1 hour', const Duration(hours: 1)),
              _buildQuickTimeChip(context, '4 hours', const Duration(hours: 4)),
              _buildQuickTimeChip(context, 'Tomorrow', const Duration(days: 1)),
              _buildQuickTimeChip(context, '1 week', const Duration(days: 7)),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isScheduling ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _isScheduling ? null : _scheduleMessage,
          child: _isScheduling
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Schedule'),
        ),
      ],
    );
  }

  Widget _buildQuickTimeChip(
      BuildContext context, String label, Duration duration) {
    final theme = Theme.of(context);

    return ActionChip(
      label: Text(label),
      onPressed: () {
        final newDateTime = DateTime.now().add(duration);
        setState(() {
          _selectedDate = newDateTime;
          _selectedTime = TimeOfDay.fromDateTime(newDateTime);
        });
      },
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      side: BorderSide(
        color: theme.colorScheme.outline.withAlpha(77),
      ),
    );
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime() async {
    final picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );

    if (picked != null) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  Future<void> _scheduleMessage() async {
    setState(() {
      _isScheduling = true;
    });

    try {
      final currentUser = ref.read(authStateProvider).user;
      if (currentUser == null) return;

      final scheduledDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _selectedTime.hour,
        _selectedTime.minute,
      );

      // Validate scheduled time
      if (scheduledDateTime.isBefore(DateTime.now())) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cannot schedule message in the past'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final scheduleNotifier = ref.read(messageScheduleProvider.notifier);
      final scheduledMessage = await scheduleNotifier.scheduleMessage(
        chatId: widget.chatId,
        senderId: currentUser.id,
        recipientId: widget.recipientId,
        text: widget.text,
        type: widget.type,
        scheduledFor: scheduledDateTime,
        mediaUrl: widget.mediaUrl,
      );

      if (scheduledMessage != null) {
        if (mounted) {
          Navigator.of(context).pop(true);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Message scheduled for ${_formatDateTime(scheduledDateTime)}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to schedule message'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } finally {
      setState(() {
        _isScheduling = false;
      });
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final selectedDay = DateTime(date.year, date.month, date.day);

    if (selectedDay == today) {
      return 'Today';
    } else if (selectedDay == tomorrow) {
      return 'Tomorrow';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final timeString = TimeOfDay.fromDateTime(dateTime).format(context);
    final dateString = _formatDate(dateTime);
    return '$dateString at $timeString';
  }

  /// Show schedule dialog
  static Future<bool?> show(
    BuildContext context, {
    required String chatId,
    required String recipientId,
    required String text,
    required MessageType type,
    String? mediaUrl,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => MessageScheduleDialog(
        chatId: chatId,
        recipientId: recipientId,
        text: text,
        type: type,
        mediaUrl: mediaUrl,
      ),
    );
  }
}
