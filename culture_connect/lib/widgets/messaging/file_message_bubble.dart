import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/messaging/file_message_model.dart';
import 'package:culture_connect/providers/messaging/file_sharing_provider.dart';
import 'package:culture_connect/widgets/messaging/file_preview_widget.dart';

/// File message bubble for chat integration
class FileMessageBubble extends ConsumerWidget {
  final FileMessageModel fileMessage;
  final bool isFromCurrentUser;
  final Color? primaryColor;
  final Color? backgroundColor;
  final VoidCallback? onLongPress;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onDownload;
  final VoidCallback? onPreview;

  const FileMessageBubble({
    super.key,
    required this.fileMessage,
    required this.isFromCurrentUser,
    this.primaryColor,
    this.backgroundColor,
    this.onLongPress,
    this.onDoubleTap,
    this.onDownload,
    this.onPreview,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final downloadState = ref.watch(fileDownloadProvider);
    
    return GestureDetector(
      onLongPress: onLongPress,
      onDoubleTap: onDoubleTap,
      child: Container(
        margin: EdgeInsets.only(
          left: isFromCurrentUser ? 64 : 16,
          right: isFromCurrentUser ? 16 : 64,
          bottom: 8,
        ),
        child: Column(
          crossAxisAlignment: isFromCurrentUser 
              ? CrossAxisAlignment.end 
              : CrossAxisAlignment.start,
          children: [
            // Message bubble
            Container(
              constraints: const BoxConstraints(
                minWidth: 200,
                maxWidth: 320,
              ),
              decoration: BoxDecoration(
                color: backgroundColor ?? (isFromCurrentUser
                    ? (primaryColor ?? theme.colorScheme.primary).withAlpha(26)
                    : theme.colorScheme.surfaceContainerHighest),
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(20),
                  topRight: const Radius.circular(20),
                  bottomLeft: Radius.circular(isFromCurrentUser ? 20 : 4),
                  bottomRight: Radius.circular(isFromCurrentUser ? 4 : 20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.shadow.withAlpha(26),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // File preview
                  GestureDetector(
                    onTap: onPreview,
                    child: FilePreviewWidget(
                      fileMessage: fileMessage,
                      height: 160,
                      showControls: false,
                    ),
                  ),
                  
                  // File info and actions
                  Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // File name
                        Text(
                          fileMessage.fileName,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        const SizedBox(height: 4),
                        
                        // File size and type
                        Row(
                          children: [
                            Text(
                              fileMessage.formattedFileSize,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: (primaryColor ?? theme.colorScheme.primary).withAlpha(26),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                fileMessage.fileExtension.toUpperCase(),
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: primaryColor ?? theme.colorScheme.primary,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 10,
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 12),
                        
                        // Action buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Download button
                            if (fileMessage.permissions.canDownload)
                              _buildDownloadButton(context, ref, downloadState),
                            
                            // Preview button
                            if (fileMessage.permissions.canPreview)
                              TextButton.icon(
                                onPressed: onPreview,
                                icon: const Icon(Icons.visibility, size: 16),
                                label: const Text('Preview'),
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 4,
                                  ),
                                  minimumSize: Size.zero,
                                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Message status and timestamp
            Padding(
              padding: const EdgeInsets.only(top: 4, left: 8, right: 8),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Upload status
                  if (fileMessage.status == FileMessageStatus.uploading)
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(
                            strokeWidth: 1.5,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Uploading...',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    )
                  else if (fileMessage.status == FileMessageStatus.failed)
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 12,
                          color: theme.colorScheme.error,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Failed',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.error,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    )
                  else
                    // Timestamp
                    Text(
                      _formatTimestamp(fileMessage.createdAt),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontSize: 10,
                      ),
                    ),
                  
                  // Message status indicators for sent messages
                  if (isFromCurrentUser && fileMessage.status == FileMessageStatus.uploaded) ...[
                    const SizedBox(width: 4),
                    Icon(
                      Icons.done_all,
                      size: 12,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDownloadButton(BuildContext context, WidgetRef ref, FileDownloadState downloadState) {
    final theme = Theme.of(context);
    final isDownloading = downloadState.isFileDownloading(fileMessage.id);
    final progress = downloadState.getDownloadProgress(fileMessage.id);
    final error = downloadState.getDownloadError(fileMessage.id);
    
    if (isDownloading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              value: progress,
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                primaryColor ?? theme.colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${(progress * 100).toInt()}%',
            style: theme.textTheme.bodySmall?.copyWith(
              color: primaryColor ?? theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    }
    
    if (error != null) {
      return TextButton.icon(
        onPressed: () => _handleDownload(ref),
        icon: Icon(
          Icons.refresh,
          size: 16,
          color: theme.colorScheme.error,
        ),
        label: Text(
          'Retry',
          style: TextStyle(color: theme.colorScheme.error),
        ),
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          minimumSize: Size.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      );
    }
    
    return TextButton.icon(
      onPressed: () => _handleDownload(ref),
      icon: Icon(
        fileMessage.isLocallyAvailable ? Icons.folder_open : Icons.download,
        size: 16,
      ),
      label: Text(fileMessage.isLocallyAvailable ? 'Open' : 'Download'),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }

  void _handleDownload(WidgetRef ref) {
    if (fileMessage.isLocallyAvailable) {
      onDownload?.call();
    } else {
      ref.read(fileDownloadProvider.notifier).downloadFile(fileMessage);
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Compact file message bubble for group chats
class CompactFileMessageBubble extends ConsumerWidget {
  final FileMessageModel fileMessage;
  final bool isFromCurrentUser;
  final String? senderName;
  final Color? primaryColor;
  final VoidCallback? onTap;

  const CompactFileMessageBubble({
    super.key,
    required this.fileMessage,
    required this.isFromCurrentUser,
    this.senderName,
    this.primaryColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Avatar placeholder
            if (!isFromCurrentUser)
              Container(
                width: 32,
                height: 32,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: primaryColor?.withAlpha(26) ?? theme.colorScheme.primary.withAlpha(26),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    senderName?.substring(0, 1).toUpperCase() ?? '?',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: primaryColor ?? theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            
            // Message content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Sender name
                  if (!isFromCurrentUser && senderName != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        senderName!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: primaryColor ?? theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  
                  // File message
                  Container(
                    constraints: const BoxConstraints(maxWidth: 280),
                    decoration: BoxDecoration(
                      color: isFromCurrentUser
                          ? (primaryColor ?? theme.colorScheme.primary).withAlpha(26)
                          : theme.colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        children: [
                          // File icon
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: (primaryColor ?? theme.colorScheme.primary).withAlpha(26),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              _getFileIcon(),
                              color: primaryColor ?? theme.colorScheme.primary,
                              size: 20,
                            ),
                          ),
                          
                          const SizedBox(width: 12),
                          
                          // File info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  fileMessage.fileName,
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  fileMessage.formattedFileSize,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // Timestamp
                  Padding(
                    padding: const EdgeInsets.only(top: 2, left: 4),
                    child: Text(
                      _formatTimestamp(fileMessage.createdAt),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getFileIcon() {
    switch (fileMessage.fileType) {
      case FileType.image:
        return Icons.image;
      case FileType.video:
        return Icons.videocam;
      case FileType.audio:
        return Icons.audiotrack;
      case FileType.document:
        return Icons.description;
      case FileType.archive:
        return Icons.archive;
      case FileType.code:
        return Icons.code;
      case FileType.other:
      default:
        return Icons.insert_drive_file;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
