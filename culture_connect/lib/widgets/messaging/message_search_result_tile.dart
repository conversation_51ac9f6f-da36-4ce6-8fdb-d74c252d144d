import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/message_search_model.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/user_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

class MessageSearchResultTile extends ConsumerWidget {
  final MessageSearchResult result;
  final VoidCallback? onTap;

  const MessageSearchResultTile({
    super.key,
    required this.result,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final senderAsync = ref.watch(userProvider(result.message.senderId));

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with sender info and timestamp
              Row(
                children: [
                  // Sender avatar and name
                  Expanded(
                    child: senderAsync.when(
                      data: (sender) => _buildSenderInfo(sender),
                      loading: () => _buildSenderInfoLoading(),
                      error: (_, __) => _buildSenderInfoError(),
                    ),
                  ),
                  
                  // Timestamp
                  Text(
                    _formatTimestamp(result.message.timestamp),
                    style: const TextStyle(
                      color: AppTheme.textSecondaryColor,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Message content with highlighting
              _buildMessageContent(),
              
              const SizedBox(height: 8),
              
              // Message metadata
              _buildMessageMetadata(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSenderInfo(UserModel? sender) {
    return Row(
      children: [
        // Avatar
        CircleAvatar(
          radius: 16,
          backgroundColor: AppTheme.primaryColor.withAlpha(51),
          backgroundImage: sender?.profilePicture != null
              ? NetworkImage(sender!.profilePicture!)
              : null,
          child: sender?.profilePicture == null
              ? Text(
                  sender?.fullName.isNotEmpty == true
                      ? sender!.fullName[0].toUpperCase()
                      : '?',
                  style: const TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                )
              : null,
        ),
        
        const SizedBox(width: 8),
        
        // Name
        Expanded(
          child: Text(
            sender?.fullName ?? 'Unknown User',
            style: const TextStyle(
              color: AppTheme.textPrimaryColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildSenderInfoLoading() {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          width: 100,
          height: 16,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ],
    );
  }

  Widget _buildSenderInfoError() {
    return const Row(
      children: [
        CircleAvatar(
          radius: 16,
          backgroundColor: Colors.grey,
          child: Icon(Icons.person, color: Colors.white, size: 16),
        ),
        SizedBox(width: 8),
        Text(
          'Unknown User',
          style: TextStyle(
            color: AppTheme.textSecondaryColor,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildMessageContent() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.primaryColor.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Message type indicator
          if (result.message.type != MessageType.text)
            Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Icon(
                    _getMessageTypeIcon(result.message.type),
                    size: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _getMessageTypeLabel(result.message.type),
                    style: const TextStyle(
                      color: AppTheme.textSecondaryColor,
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          
          // Highlighted message text
          _buildHighlightedText(),
        ],
      ),
    );
  }

  Widget _buildHighlightedText() {
    // Parse the highlighted text and create rich text
    final text = result.highlightedText;
    final spans = <TextSpan>[];
    
    // Simple highlighting implementation
    // In a real app, you'd use a more sophisticated highlighting system
    final parts = text.split('**');
    for (int i = 0; i < parts.length; i++) {
      if (i % 2 == 0) {
        // Normal text
        spans.add(TextSpan(
          text: parts[i],
          style: const TextStyle(
            color: AppTheme.textPrimaryColor,
            fontSize: 14,
          ),
        ));
      } else {
        // Highlighted text
        spans.add(TextSpan(
          text: parts[i],
          style: const TextStyle(
            color: AppTheme.primaryColor,
            fontSize: 14,
            fontWeight: FontWeight.w600,
            backgroundColor: Colors.yellow,
          ),
        ));
      }
    }

    return RichText(
      text: TextSpan(children: spans),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildMessageMetadata() {
    return Row(
      children: [
        // Relevance score
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha(26),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            'Score: ${result.relevanceScore.toStringAsFixed(1)}',
            style: const TextStyle(
              color: AppTheme.primaryColor,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        
        const SizedBox(width: 8),
        
        // Message status
        if (result.message.status != MessageStatus.sent)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getStatusColor(result.message.status).withAlpha(26),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              _getStatusLabel(result.message.status),
              style: TextStyle(
                color: _getStatusColor(result.message.status),
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        
        const Spacer(),
        
        // Match count
        if (result.matchPositions.isNotEmpty)
          Text(
            '${result.matchPositions.length} match${result.matchPositions.length == 1 ? '' : 'es'}',
            style: const TextStyle(
              color: AppTheme.textSecondaryColor,
              fontSize: 10,
            ),
          ),
      ],
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return DateFormat('MMM d, y').format(timestamp);
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  IconData _getMessageTypeIcon(MessageType type) {
    switch (type) {
      case MessageType.image:
        return Icons.image;
      case MessageType.video:
        return Icons.videocam;
      case MessageType.audio:
        return Icons.mic;
      case MessageType.location:
        return Icons.location_on;
      case MessageType.contact:
        return Icons.contact_phone;
      case MessageType.file:
        return Icons.attach_file;
      case MessageType.system:
        return Icons.info;
      case MessageType.text:
      default:
        return Icons.message;
    }
  }

  String _getMessageTypeLabel(MessageType type) {
    switch (type) {
      case MessageType.image:
        return 'Image';
      case MessageType.video:
        return 'Video';
      case MessageType.audio:
        return 'Audio';
      case MessageType.location:
        return 'Location';
      case MessageType.contact:
        return 'Contact';
      case MessageType.file:
        return 'File';
      case MessageType.system:
        return 'System';
      case MessageType.text:
      default:
        return 'Text';
    }
  }

  Color _getStatusColor(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return Colors.orange;
      case MessageStatus.sent:
        return Colors.green;
      case MessageStatus.delivered:
        return Colors.blue;
      case MessageStatus.read:
        return AppTheme.primaryColor;
      case MessageStatus.failed:
        return Colors.red;
      case MessageStatus.pending:
        return Colors.grey;
      case MessageStatus.reported:
        return Colors.red;
      case MessageStatus.blocked:
        return Colors.red;
    }
  }

  String _getStatusLabel(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return 'Sending';
      case MessageStatus.sent:
        return 'Sent';
      case MessageStatus.delivered:
        return 'Delivered';
      case MessageStatus.read:
        return 'Read';
      case MessageStatus.failed:
        return 'Failed';
      case MessageStatus.pending:
        return 'Pending';
      case MessageStatus.reported:
        return 'Reported';
      case MessageStatus.blocked:
        return 'Blocked';
    }
  }
}
