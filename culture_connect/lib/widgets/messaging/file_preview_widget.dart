import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/messaging/file_message_model.dart';
import 'package:culture_connect/providers/messaging/file_sharing_provider.dart';

/// Widget for previewing different file types
class FilePreviewWidget extends ConsumerWidget {
  final FileMessageModel fileMessage;
  final double? width;
  final double? height;
  final bool showControls;
  final VoidCallback? onTap;

  const FilePreviewWidget({
    super.key,
    required this.fileMessage,
    this.width,
    this.height,
    this.showControls = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: theme.colorScheme.outline.withAlpha(77),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: _buildPreviewContent(context, ref),
        ),
      ),
    );
  }

  Widget _buildPreviewContent(BuildContext context, WidgetRef ref) {
    switch (fileMessage.fileType) {
      case FileType.image:
        return _buildImagePreview(context, ref);
      case FileType.video:
        return _buildVideoPreview(context, ref);
      case FileType.audio:
        return _buildAudioPreview(context, ref);
      case FileType.document:
        return _buildDocumentPreview(context, ref);
      case FileType.archive:
        return _buildArchivePreview(context, ref);
      case FileType.code:
        return _buildCodePreview(context, ref);
      case FileType.other:
      default:
        return _buildGenericPreview(context, ref);
    }
  }

  Widget _buildImagePreview(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    
    if (fileMessage.hasThumbnail) {
      return Stack(
        fit: StackFit.expand,
        children: [
          // Thumbnail image
          Image.network(
            fileMessage.thumbnailUrl!,
            fit: BoxFit.cover,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                      : null,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return _buildGenericPreview(context, ref);
            },
          ),
          
          // Overlay with file info
          if (showControls)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withAlpha(128),
                    ],
                  ),
                ),
                child: Text(
                  fileMessage.fileName,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
        ],
      );
    }

    return _buildGenericPreview(context, ref);
  }

  Widget _buildVideoPreview(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    
    if (fileMessage.hasThumbnail) {
      return Stack(
        fit: StackFit.expand,
        children: [
          // Video thumbnail
          Image.network(
            fileMessage.thumbnailUrl!,
            fit: BoxFit.cover,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                      : null,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return _buildGenericPreview(context, ref);
            },
          ),
          
          // Play button overlay
          Center(
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(128),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 32,
              ),
            ),
          ),
          
          // File info overlay
          if (showControls)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withAlpha(128),
                    ],
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        fileMessage.fileName,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      fileMessage.formattedFileSize,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withAlpha(179),
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      );
    }

    return _buildGenericPreview(context, ref);
  }

  Widget _buildAudioPreview(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.audiotrack,
            size: 48,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 12),
          Text(
            fileMessage.fileName,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            fileMessage.formattedFileSize,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentPreview(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final extension = fileMessage.fileExtension.toUpperCase();
    
    IconData iconData;
    Color iconColor;
    
    switch (extension) {
      case 'PDF':
        iconData = Icons.picture_as_pdf;
        iconColor = Colors.red;
        break;
      case 'DOC':
      case 'DOCX':
        iconData = Icons.description;
        iconColor = Colors.blue;
        break;
      case 'XLS':
      case 'XLSX':
        iconData = Icons.table_chart;
        iconColor = Colors.green;
        break;
      case 'PPT':
      case 'PPTX':
        iconData = Icons.slideshow;
        iconColor = Colors.orange;
        break;
      case 'TXT':
        iconData = Icons.text_snippet;
        iconColor = theme.colorScheme.primary;
        break;
      default:
        iconData = Icons.insert_drive_file;
        iconColor = theme.colorScheme.primary;
    }
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            iconData,
            size: 48,
            color: iconColor,
          ),
          const SizedBox(height: 12),
          Text(
            fileMessage.fileName,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            fileMessage.formattedFileSize,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          if (extension.isNotEmpty) ...[
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: iconColor.withAlpha(26),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                extension,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: iconColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildArchivePreview(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final extension = fileMessage.fileExtension.toUpperCase();
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.archive,
            size: 48,
            color: theme.colorScheme.secondary,
          ),
          const SizedBox(height: 12),
          Text(
            fileMessage.fileName,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            fileMessage.formattedFileSize,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          if (extension.isNotEmpty) ...[
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: theme.colorScheme.secondary.withAlpha(26),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                extension,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.secondary,
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCodePreview(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final extension = fileMessage.fileExtension.toUpperCase();
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.code,
            size: 48,
            color: theme.colorScheme.tertiary,
          ),
          const SizedBox(height: 12),
          Text(
            fileMessage.fileName,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            fileMessage.formattedFileSize,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          if (extension.isNotEmpty) ...[
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: theme.colorScheme.tertiary.withAlpha(26),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                extension,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.tertiary,
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGenericPreview(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.insert_drive_file,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 12),
          Text(
            fileMessage.fileName,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            fileMessage.formattedFileSize,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}
