import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/translation/enhanced_pronunciation_guidance.dart';
import 'package:culture_connect/services/voice_translation/enhanced_pronunciation_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/providers/voice_translation/translation_accuracy_provider.dart';

/// Provider for EnhancedPronunciationService
final enhancedPronunciationServiceProvider = Provider<EnhancedPronunciationService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final analyticsService = ref.watch(analyticsServiceProvider);
  
  return EnhancedPronunciationService(prefs, loggingService, analyticsService);
});

/// State class for enhanced pronunciation guidance
class EnhancedPronunciationState {
  final Map<String, EnhancedPronunciationGuidance> guidanceMap;
  final EnhancedPronunciationGuidance? currentGuidance;
  final List<PronunciationProgress> practiceHistory;
  final bool isLoading;
  final String? error;

  const EnhancedPronunciationState({
    this.guidanceMap = const {},
    this.currentGuidance,
    this.practiceHistory = const [],
    this.isLoading = false,
    this.error,
  });

  EnhancedPronunciationState copyWith({
    Map<String, EnhancedPronunciationGuidance>? guidanceMap,
    EnhancedPronunciationGuidance? currentGuidance,
    List<PronunciationProgress>? practiceHistory,
    bool? isLoading,
    String? error,
  }) {
    return EnhancedPronunciationState(
      guidanceMap: guidanceMap ?? this.guidanceMap,
      currentGuidance: currentGuidance ?? this.currentGuidance,
      practiceHistory: practiceHistory ?? this.practiceHistory,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// StateNotifier for managing enhanced pronunciation guidance
class EnhancedPronunciationNotifier extends StateNotifier<EnhancedPronunciationState> {
  final EnhancedPronunciationService _service;

  EnhancedPronunciationNotifier(this._service) : super(const EnhancedPronunciationState()) {
    _initialize();
  }

  /// Initialize the notifier and listen to service streams
  void _initialize() {
    // Listen to guidance updates
    _service.guidanceStream.listen(
      (guidance) {
        if (mounted) {
          final updatedGuidanceMap = Map<String, EnhancedPronunciationGuidance>.from(state.guidanceMap);
          updatedGuidanceMap[guidance.id] = guidance;
          
          state = state.copyWith(
            guidanceMap: updatedGuidanceMap,
            currentGuidance: guidance,
            error: null,
          );
        }
      },
      onError: (error) {
        if (mounted) {
          state = state.copyWith(
            error: error.toString(),
            isLoading: false,
          );
        }
      },
    );

    // Listen to practice progress updates
    _service.progressStream.listen(
      (progress) {
        if (mounted) {
          final updatedHistory = [...state.practiceHistory, progress];
          state = state.copyWith(
            practiceHistory: updatedHistory,
            error: null,
          );
        }
      },
      onError: (error) {
        if (mounted) {
          state = state.copyWith(
            error: error.toString(),
          );
        }
      },
    );

    _loadPracticeHistory();
  }

  /// Load practice history from service
  Future<void> _loadPracticeHistory() async {
    try {
      final history = await _service.getPracticeHistory();
      if (mounted) {
        state = state.copyWith(practiceHistory: history);
      }
    } catch (error) {
      if (mounted) {
        state = state.copyWith(error: error.toString());
      }
    }
  }

  /// Generate pronunciation guidance for text
  Future<EnhancedPronunciationGuidance> generatePronunciationGuidance({
    required String text,
    required String languageCode,
    String? dialect,
    PronunciationDifficulty? targetDifficulty,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final guidance = await _service.generatePronunciationGuidance(
        text: text,
        languageCode: languageCode,
        dialect: dialect,
        targetDifficulty: targetDifficulty,
      );

      state = state.copyWith(isLoading: false);
      return guidance;
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
      rethrow;
    }
  }

  /// Record practice session
  Future<PronunciationProgress> recordPracticeSession({
    required String guidanceId,
    required double accuracyScore,
    required int attemptCount,
    required Duration practiceTime,
    List<String>? improvedSounds,
    List<String>? needsWorkSounds,
  }) async {
    try {
      final progress = await _service.recordPracticeSession(
        guidanceId: guidanceId,
        accuracyScore: accuracyScore,
        attemptCount: attemptCount,
        practiceTime: practiceTime,
        improvedSounds: improvedSounds,
        needsWorkSounds: needsWorkSounds,
      );

      return progress;
    } catch (error) {
      state = state.copyWith(error: error.toString());
      rethrow;
    }
  }

  /// Get pronunciation guidance by ID
  EnhancedPronunciationGuidance? getPronunciationGuidance(String id) {
    return state.guidanceMap[id];
  }

  /// Get guidance by language
  List<EnhancedPronunciationGuidance> getGuidanceByLanguage(String languageCode) {
    return state.guidanceMap.values
        .where((guidance) => guidance.languageCode == languageCode)
        .toList();
  }

  /// Get guidance by difficulty
  List<EnhancedPronunciationGuidance> getGuidanceByDifficulty(PronunciationDifficulty difficulty) {
    return state.guidanceMap.values
        .where((guidance) => guidance.difficulty == difficulty)
        .toList();
  }

  /// Get recent practice sessions
  List<PronunciationProgress> getRecentPractice({int limit = 10}) {
    final sortedHistory = [...state.practiceHistory]
      ..sort((a, b) => b.practiceDate.compareTo(a.practiceDate));
    return sortedHistory.take(limit).toList();
  }

  /// Get practice history for a specific guidance
  List<PronunciationProgress> getPracticeHistoryForGuidance(String guidanceId) {
    final guidance = state.guidanceMap[guidanceId];
    return guidance?.practiceHistory ?? [];
  }

  /// Get average accuracy score
  double getAverageAccuracy() {
    if (state.practiceHistory.isEmpty) return 0.0;
    
    final totalScore = state.practiceHistory.fold<double>(
      0.0,
      (sum, progress) => sum + progress.accuracyScore,
    );
    
    return totalScore / state.practiceHistory.length;
  }

  /// Get total practice time
  Duration getTotalPracticeTime() {
    return state.practiceHistory.fold<Duration>(
      Duration.zero,
      (total, progress) => total + progress.practiceTime,
    );
  }

  /// Get sounds that need work
  List<String> getSoundsNeedingWork() {
    final soundCounts = <String, int>{};
    
    for (final progress in state.practiceHistory) {
      for (final sound in progress.needsWorkSounds) {
        soundCounts[sound] = (soundCounts[sound] ?? 0) + 1;
      }
    }
    
    final sortedSounds = soundCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedSounds.map((entry) => entry.key).toList();
  }

  /// Get improved sounds
  List<String> getImprovedSounds() {
    final soundCounts = <String, int>{};
    
    for (final progress in state.practiceHistory) {
      for (final sound in progress.improvedSounds) {
        soundCounts[sound] = (soundCounts[sound] ?? 0) + 1;
      }
    }
    
    final sortedSounds = soundCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedSounds.map((entry) => entry.key).toList();
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear current guidance
  void clearCurrentGuidance() {
    state = state.copyWith(currentGuidance: null);
  }

  @override
  void dispose() {
    _service.dispose();
    super.dispose();
  }
}

/// Provider for EnhancedPronunciationNotifier
final enhancedPronunciationProvider = StateNotifierProvider<EnhancedPronunciationNotifier, EnhancedPronunciationState>((ref) {
  final service = ref.watch(enhancedPronunciationServiceProvider);
  return EnhancedPronunciationNotifier(service);
});

/// Provider for pronunciation guidance map
final pronunciationGuidanceMapProvider = Provider<Map<String, EnhancedPronunciationGuidance>>((ref) {
  return ref.watch(enhancedPronunciationProvider).guidanceMap;
});

/// Provider for current pronunciation guidance
final currentPronunciationGuidanceProvider = Provider<EnhancedPronunciationGuidance?>((ref) {
  return ref.watch(enhancedPronunciationProvider).currentGuidance;
});

/// Provider for practice history
final practiceHistoryProvider = Provider<List<PronunciationProgress>>((ref) {
  return ref.watch(enhancedPronunciationProvider).practiceHistory;
});

/// Provider for pronunciation loading state
final pronunciationLoadingProvider = Provider<bool>((ref) {
  return ref.watch(enhancedPronunciationProvider).isLoading;
});

/// Provider for pronunciation error state
final pronunciationErrorProvider = Provider<String?>((ref) {
  return ref.watch(enhancedPronunciationProvider).error;
});

/// Provider for pronunciation guidance by ID
final pronunciationGuidanceByIdProvider = Provider.family<EnhancedPronunciationGuidance?, String>((ref, id) {
  final notifier = ref.watch(enhancedPronunciationProvider.notifier);
  return notifier.getPronunciationGuidance(id);
});

/// Provider for guidance by language
final guidanceByLanguageProvider = Provider.family<List<EnhancedPronunciationGuidance>, String>((ref, languageCode) {
  final notifier = ref.watch(enhancedPronunciationProvider.notifier);
  return notifier.getGuidanceByLanguage(languageCode);
});

/// Provider for guidance by difficulty
final guidanceByDifficultyProvider = Provider.family<List<EnhancedPronunciationGuidance>, PronunciationDifficulty>((ref, difficulty) {
  final notifier = ref.watch(enhancedPronunciationProvider.notifier);
  return notifier.getGuidanceByDifficulty(difficulty);
});

/// Provider for recent practice sessions
final recentPracticeProvider = Provider.family<List<PronunciationProgress>, int>((ref, limit) {
  final notifier = ref.watch(enhancedPronunciationProvider.notifier);
  return notifier.getRecentPractice(limit: limit);
});

/// Provider for practice statistics
final practiceStatisticsProvider = Provider<PracticeStatistics>((ref) {
  final notifier = ref.watch(enhancedPronunciationProvider.notifier);
  final practiceHistory = ref.watch(practiceHistoryProvider);
  
  if (practiceHistory.isEmpty) {
    return const PracticeStatistics(
      totalSessions: 0,
      averageAccuracy: 0.0,
      totalPracticeTime: Duration.zero,
      soundsNeedingWork: [],
      improvedSounds: [],
      accuracyTrend: 0.0,
    );
  }

  final averageAccuracy = notifier.getAverageAccuracy();
  final totalPracticeTime = notifier.getTotalPracticeTime();
  final soundsNeedingWork = notifier.getSoundsNeedingWork();
  final improvedSounds = notifier.getImprovedSounds();

  // Calculate accuracy trend (last 5 sessions vs previous 5)
  double accuracyTrend = 0.0;
  if (practiceHistory.length >= 10) {
    final recentSessions = practiceHistory.take(5).toList();
    final previousSessions = practiceHistory.skip(5).take(5).toList();
    
    final recentAvg = recentSessions.fold<double>(0.0, (sum, p) => sum + p.accuracyScore) / 5;
    final previousAvg = previousSessions.fold<double>(0.0, (sum, p) => sum + p.accuracyScore) / 5;
    
    accuracyTrend = recentAvg - previousAvg;
  }

  return PracticeStatistics(
    totalSessions: practiceHistory.length,
    averageAccuracy: averageAccuracy,
    totalPracticeTime: totalPracticeTime,
    soundsNeedingWork: soundsNeedingWork,
    improvedSounds: improvedSounds,
    accuracyTrend: accuracyTrend,
  );
});

/// Provider for pronunciation difficulty distribution
final difficultyDistributionProvider = Provider<Map<PronunciationDifficulty, int>>((ref) {
  final guidanceMap = ref.watch(pronunciationGuidanceMapProvider);
  final distribution = <PronunciationDifficulty, int>{};
  
  for (final guidance in guidanceMap.values) {
    distribution[guidance.difficulty] = (distribution[guidance.difficulty] ?? 0) + 1;
  }
  
  return distribution;
});

/// Provider for language distribution
final languageDistributionProvider = Provider<Map<String, int>>((ref) {
  final guidanceMap = ref.watch(pronunciationGuidanceMapProvider);
  final distribution = <String, int>{};
  
  for (final guidance in guidanceMap.values) {
    distribution[guidance.languageCode] = (distribution[guidance.languageCode] ?? 0) + 1;
  }
  
  return distribution;
});

/// Model for practice statistics
class PracticeStatistics {
  final int totalSessions;
  final double averageAccuracy;
  final Duration totalPracticeTime;
  final List<String> soundsNeedingWork;
  final List<String> improvedSounds;
  final double accuracyTrend;

  const PracticeStatistics({
    required this.totalSessions,
    required this.averageAccuracy,
    required this.totalPracticeTime,
    required this.soundsNeedingWork,
    required this.improvedSounds,
    required this.accuracyTrend,
  });

  /// Get accuracy percentage
  double get accuracyPercentage => averageAccuracy * 100;

  /// Check if accuracy is improving
  bool get isImproving => accuracyTrend > 0;

  /// Get accuracy trend percentage
  double get accuracyTrendPercentage => accuracyTrend * 100;

  /// Get total practice time in hours
  double get totalPracticeHours => totalPracticeTime.inMinutes / 60.0;

  /// Get average session duration
  Duration get averageSessionDuration {
    if (totalSessions == 0) return Duration.zero;
    return Duration(milliseconds: totalPracticeTime.inMilliseconds ~/ totalSessions);
  }

  /// Get top sounds needing work
  List<String> getTopSoundsNeedingWork({int limit = 5}) {
    return soundsNeedingWork.take(limit).toList();
  }

  /// Get top improved sounds
  List<String> getTopImprovedSounds({int limit = 5}) {
    return improvedSounds.take(limit).toList();
  }
}
