import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/message_search_model.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/messaging/message_search_provider.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/widgets/messaging/message_search_result_tile.dart';
import 'package:culture_connect/widgets/messaging/message_search_filters_sheet.dart';
import 'package:culture_connect/widgets/common/app_gradient_background.dart';
import 'package:culture_connect/theme/app_theme.dart';

class MessageSearchScreen extends ConsumerStatefulWidget {
  final String? chatId;
  final String? initialQuery;

  const MessageSearchScreen({
    super.key,
    this.chatId,
    this.initialQuery,
  });

  @override
  ConsumerState<MessageSearchScreen> createState() => _MessageSearchScreenState();
}

class _MessageSearchScreenState extends ConsumerState<MessageSearchScreen> {
  late TextEditingController _searchController;
  final FocusNode _searchFocusNode = FocusNode();
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialQuery ?? '');
    
    if (widget.initialQuery?.isNotEmpty == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _performSearch();
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _performSearch() {
    final query = _searchController.text.trim();
    if (query.isEmpty) {
      ref.read(messageSearchProvider.notifier).clearResults();
      ref.read(globalMessageSearchProvider.notifier).clearResults();
      return;
    }

    final filters = ref.read(searchFiltersProvider);
    final sortOptions = ref.read(searchSortProvider);
    
    final searchQuery = MessageSearchQuery(
      query: query,
      filters: filters,
      sortBy: sortOptions['sortBy'] as String,
      sortAscending: sortOptions['sortAscending'] as bool,
    );

    if (widget.chatId != null) {
      // Search in specific chat
      ref.read(messageSearchProvider.notifier).searchMessages(
        chatId: widget.chatId!,
        searchQuery: searchQuery,
      );
    } else {
      // Global search - get user's chat IDs first
      final currentUserAsync = ref.read(currentUserModelProvider);
      currentUserAsync.whenData((user) {
        if (user != null) {
          // In a real implementation, you'd get the user's chat IDs
          // For now, we'll use an empty list as placeholder
          final chatIds = <String>[]; // TODO: Get actual chat IDs
          ref.read(globalMessageSearchProvider.notifier).searchAllChats(
            chatIds: chatIds,
            searchQuery: searchQuery,
          );
        }
      });
    }

    setState(() {
      _showSuggestions = false;
    });
  }

  void _showFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const MessageSearchFiltersSheet(),
    ).then((_) {
      // Refresh search if filters changed
      if (_searchController.text.trim().isNotEmpty) {
        _performSearch();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final searchResults = ref.watch(messageSearchProvider);
    final globalSearchResults = ref.watch(globalMessageSearchProvider);
    final searchHistory = ref.watch(searchHistoryProvider);
    final isLoading = ref.watch(searchLoadingProvider);
    final resultsCount = ref.watch(searchResultsCountProvider);

    return Scaffold(
      body: AppGradientBackground(
        child: SafeArea(
          child: Column(
            children: [
              // Search Header
              _buildSearchHeader(),
              
              // Search Results or Suggestions
              Expanded(
                child: _showSuggestions
                    ? _buildSearchSuggestions(searchHistory)
                    : _buildSearchResults(searchResults, globalSearchResults),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Top bar with back button and title
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.arrow_back, color: Colors.white),
              ),
              Expanded(
                child: Text(
                  widget.chatId != null ? 'Search in Chat' : 'Search Messages',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              IconButton(
                onPressed: _showFilters,
                icon: const Icon(Icons.tune, color: Colors.white),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Search bar
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                hintText: 'Search messages...',
                prefixIcon: const Icon(Icons.search, color: AppTheme.textSecondaryColor),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          ref.read(messageSearchProvider.notifier).clearResults();
                          ref.read(globalMessageSearchProvider.notifier).clearResults();
                          setState(() {
                            _showSuggestions = false;
                          });
                        },
                        icon: const Icon(Icons.clear, color: AppTheme.textSecondaryColor),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _showSuggestions = value.isNotEmpty;
                });
                
                // Debounce search
                Future.delayed(const Duration(milliseconds: 500), () {
                  if (_searchController.text == value && value.isNotEmpty) {
                    _performSearch();
                  }
                });
              },
              onSubmitted: (_) => _performSearch(),
              onTap: () {
                setState(() {
                  _showSuggestions = _searchController.text.isNotEmpty;
                });
              },
            ),
          ),
          
          // Results count
          if (resultsCount > 0)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                '$resultsCount result${resultsCount == 1 ? '' : 's'} found',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchSuggestions(AsyncValue<List<String>> searchHistory) {
    return searchHistory.when(
      data: (history) {
        if (history.isEmpty) {
          return const Center(
            child: Text(
              'Start typing to search messages',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
            ),
          );
        }

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsets.all(16),
                child: Text(
                  'Recent Searches',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: history.length,
                  itemBuilder: (context, index) {
                    final query = history[index];
                    return ListTile(
                      leading: const Icon(Icons.history, color: AppTheme.textSecondaryColor),
                      title: Text(query),
                      trailing: IconButton(
                        onPressed: () {
                          // Remove from history
                          // TODO: Implement remove from history
                        },
                        icon: const Icon(Icons.close, size: 20),
                      ),
                      onTap: () {
                        _searchController.text = query;
                        _performSearch();
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text(
          'Error loading search history',
          style: TextStyle(color: Colors.red[300]),
        ),
      ),
    );
  }

  Widget _buildSearchResults(
    AsyncValue<List<MessageSearchResult>> searchResults,
    AsyncValue<Map<String, List<MessageSearchResult>>> globalSearchResults,
  ) {
    if (widget.chatId != null) {
      // Single chat search results
      return searchResults.when(
        data: (results) => _buildResultsList(results),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text(
            'Search failed: $error',
            style: TextStyle(color: Colors.red[300]),
          ),
        ),
      );
    } else {
      // Global search results
      return globalSearchResults.when(
        data: (results) => _buildGlobalResultsList(results),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text(
            'Search failed: $error',
            style: TextStyle(color: Colors.red[300]),
          ),
        ),
      );
    }
  }

  Widget _buildResultsList(List<MessageSearchResult> results) {
    if (results.isEmpty) {
      return const Center(
        child: Text(
          'No messages found',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 16,
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: results.length,
        itemBuilder: (context, index) {
          final result = results[index];
          return MessageSearchResultTile(
            result: result,
            onTap: () {
              // Navigate to message in chat
              Navigator.of(context).pop(result.message);
            },
          );
        },
      ),
    );
  }

  Widget _buildGlobalResultsList(Map<String, List<MessageSearchResult>> results) {
    if (results.isEmpty) {
      return const Center(
        child: Text(
          'No messages found',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 16,
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: results.length,
        itemBuilder: (context, index) {
          final chatId = results.keys.elementAt(index);
          final chatResults = results[chatId]!;
          
          return ExpansionTile(
            title: Text('Chat $chatId'), // TODO: Get actual chat name
            subtitle: Text('${chatResults.length} result${chatResults.length == 1 ? '' : 's'}'),
            children: chatResults.map((result) {
              return MessageSearchResultTile(
                result: result,
                onTap: () {
                  // Navigate to message in chat
                  Navigator.of(context).pop({
                    'chatId': chatId,
                    'message': result.message,
                  });
                },
              );
            }).toList(),
          );
        },
      ),
    );
  }
}
