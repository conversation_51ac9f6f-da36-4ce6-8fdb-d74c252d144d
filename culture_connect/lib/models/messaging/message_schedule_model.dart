import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/message_model.dart';

/// Model for scheduled messages
class MessageScheduleModel {
  final String id;
  final String chatId;
  final String senderId;
  final String recipientId;
  final String text;
  final MessageType type;
  final String? mediaUrl;
  final DateTime scheduledFor;
  final DateTime createdAt;
  final MessageScheduleStatus status;
  final String? failureReason;
  final int retryCount;
  final DateTime? sentAt;
  final String? sentMessageId;
  final Map<String, dynamic>? metadata;

  const MessageScheduleModel({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.recipientId,
    required this.text,
    required this.type,
    this.mediaUrl,
    required this.scheduledFor,
    required this.createdAt,
    required this.status,
    this.failureReason,
    this.retryCount = 0,
    this.sentAt,
    this.sentMessageId,
    this.metadata,
  });

  /// Create from JSON (Firestore)
  factory MessageScheduleModel.fromJson(Map<String, dynamic> json) {
    return MessageScheduleModel(
      id: json['id'] ?? '',
      chatId: json['chatId'] ?? '',
      senderId: json['senderId'] ?? '',
      recipientId: json['recipientId'] ?? '',
      text: json['text'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => MessageType.text,
      ),
      mediaUrl: json['mediaUrl'],
      scheduledFor: (json['scheduledFor'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdAt: (json['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      status: MessageScheduleStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => MessageScheduleStatus.pending,
      ),
      failureReason: json['failureReason'],
      retryCount: json['retryCount'] ?? 0,
      sentAt: (json['sentAt'] as Timestamp?)?.toDate(),
      sentMessageId: json['sentMessageId'],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chatId': chatId,
      'senderId': senderId,
      'recipientId': recipientId,
      'text': text,
      'type': type.toString().split('.').last,
      'mediaUrl': mediaUrl,
      'scheduledFor': Timestamp.fromDate(scheduledFor),
      'createdAt': Timestamp.fromDate(createdAt),
      'status': status.toString().split('.').last,
      'failureReason': failureReason,
      'retryCount': retryCount,
      'sentAt': sentAt != null ? Timestamp.fromDate(sentAt!) : null,
      'sentMessageId': sentMessageId,
      'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  MessageScheduleModel copyWith({
    String? id,
    String? chatId,
    String? senderId,
    String? recipientId,
    String? text,
    MessageType? type,
    String? mediaUrl,
    DateTime? scheduledFor,
    DateTime? createdAt,
    MessageScheduleStatus? status,
    String? failureReason,
    int? retryCount,
    DateTime? sentAt,
    String? sentMessageId,
    Map<String, dynamic>? metadata,
  }) {
    return MessageScheduleModel(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      recipientId: recipientId ?? this.recipientId,
      text: text ?? this.text,
      type: type ?? this.type,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      scheduledFor: scheduledFor ?? this.scheduledFor,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      failureReason: failureReason ?? this.failureReason,
      retryCount: retryCount ?? this.retryCount,
      sentAt: sentAt ?? this.sentAt,
      sentMessageId: sentMessageId ?? this.sentMessageId,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert to MessageModel for sending
  MessageModel toMessageModel() {
    return MessageModel(
      id: sentMessageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      chatId: chatId,
      senderId: senderId,
      recipientId: recipientId,
      text: text,
      timestamp: sentAt ?? DateTime.now(),
      status: MessageStatus.sending,
      type: type,
      mediaUrl: mediaUrl,
      metadata: {
        ...?metadata,
        'scheduledMessageId': id,
        'originalScheduledFor': scheduledFor.toIso8601String(),
      },
    );
  }

  /// Check if message is ready to be sent
  bool get isReadyToSend {
    return status == MessageScheduleStatus.pending && 
           DateTime.now().isAfter(scheduledFor);
  }

  /// Check if message can be cancelled
  bool get canCancel {
    return status == MessageScheduleStatus.pending;
  }

  /// Check if message can be rescheduled
  bool get canReschedule {
    return status == MessageScheduleStatus.pending || 
           status == MessageScheduleStatus.failed;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageScheduleModel &&
        other.id == id &&
        other.chatId == chatId;
  }

  @override
  int get hashCode => Object.hash(id, chatId);

  @override
  String toString() {
    return 'MessageScheduleModel(id: $id, scheduledFor: $scheduledFor, status: $status)';
  }
}

/// Status of scheduled messages
enum MessageScheduleStatus {
  pending,    // Waiting to be sent
  sending,    // Currently being sent
  sent,       // Successfully sent
  failed,     // Failed to send
  cancelled,  // Cancelled by user
  expired,    // Expired (too old to send)
}

/// Extension for MessageScheduleStatus
extension MessageScheduleStatusExtension on MessageScheduleStatus {
  String get displayName {
    switch (this) {
      case MessageScheduleStatus.pending:
        return 'Scheduled';
      case MessageScheduleStatus.sending:
        return 'Sending';
      case MessageScheduleStatus.sent:
        return 'Sent';
      case MessageScheduleStatus.failed:
        return 'Failed';
      case MessageScheduleStatus.cancelled:
        return 'Cancelled';
      case MessageScheduleStatus.expired:
        return 'Expired';
    }
  }

  String get description {
    switch (this) {
      case MessageScheduleStatus.pending:
        return 'Message scheduled and waiting to be sent';
      case MessageScheduleStatus.sending:
        return 'Message is currently being sent';
      case MessageScheduleStatus.sent:
        return 'Message sent successfully';
      case MessageScheduleStatus.failed:
        return 'Failed to send message';
      case MessageScheduleStatus.cancelled:
        return 'Message cancelled by user';
      case MessageScheduleStatus.expired:
        return 'Message expired and was not sent';
    }
  }

  bool get isActive {
    return this == MessageScheduleStatus.pending || 
           this == MessageScheduleStatus.sending;
  }

  bool get isCompleted {
    return this == MessageScheduleStatus.sent || 
           this == MessageScheduleStatus.cancelled || 
           this == MessageScheduleStatus.expired;
  }

  bool get canRetry {
    return this == MessageScheduleStatus.failed;
  }
}
