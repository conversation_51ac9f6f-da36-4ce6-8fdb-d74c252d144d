import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:path_provider/path_provider.dart';
import 'package:culture_connect/models/translation/enhanced_pronunciation_guidance.dart';
import 'package:culture_connect/models/translation/pronunciation_model.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/analytics_service.dart';

/// Enhanced service for pronunciation guidance and practice
class EnhancedPronunciationService {
  final SharedPreferences _prefs;
  final LoggingService _loggingService;
  final AnalyticsService _analyticsService;

  /// Cache of pronunciation guidance
  final Map<String, EnhancedPronunciationGuidance> _guidanceCache = {};

  /// Stream controller for guidance updates
  final StreamController<EnhancedPronunciationGuidance> _guidanceController =
      StreamController<EnhancedPronunciationGuidance>.broadcast();

  /// Stream controller for practice progress
  final StreamController<PronunciationProgress> _progressController =
      StreamController<PronunciationProgress>.broadcast();

  /// Phonetic rules for different languages
  Map<String, PhoneticRules> _phoneticRules = {};

  /// Common pronunciation mistakes database
  Map<String, List<PronunciationMistake>> _commonMistakes = {};

  EnhancedPronunciationService(
    this._prefs,
    this._loggingService,
    this._analyticsService,
  ) {
    _loadPronunciationData();
  }

  /// Stream of pronunciation guidance updates
  Stream<EnhancedPronunciationGuidance> get guidanceStream => _guidanceController.stream;

  /// Stream of practice progress updates
  Stream<PronunciationProgress> get progressStream => _progressController.stream;

  /// Load pronunciation data from storage
  Future<void> _loadPronunciationData() async {
    try {
      // Load phonetic rules
      final rulesJson = _prefs.getString('phonetic_rules');
      if (rulesJson != null) {
        final Map<String, dynamic> data = jsonDecode(rulesJson);
        _phoneticRules = data.map((key, value) => 
            MapEntry(key, PhoneticRules.fromJson(value as Map<String, dynamic>)));
      } else {
        _initializeDefaultPhoneticRules();
      }

      // Load common mistakes
      final mistakesJson = _prefs.getString('common_pronunciation_mistakes');
      if (mistakesJson != null) {
        final Map<String, dynamic> data = jsonDecode(mistakesJson);
        _commonMistakes = data.map((key, value) => MapEntry(
          key,
          (value as List<dynamic>)
              .map((mistake) => PronunciationMistake.fromJson(mistake as Map<String, dynamic>))
              .toList(),
        ));
      } else {
        _initializeDefaultMistakes();
      }
    } catch (e) {
      _loggingService.warning('Error loading pronunciation data: $e');
      _initializeDefaultPhoneticRules();
      _initializeDefaultMistakes();
    }
  }

  /// Generate enhanced pronunciation guidance for text
  Future<EnhancedPronunciationGuidance> generatePronunciationGuidance({
    required String text,
    required String languageCode,
    String? dialect,
    PronunciationDifficulty? targetDifficulty,
  }) async {
    try {
      final guidanceId = const Uuid().v4();
      final cacheKey = '$languageCode-${dialect ?? 'default'}-${text.hashCode}';

      // Check cache first
      if (_guidanceCache.containsKey(cacheKey)) {
        return _guidanceCache[cacheKey]!;
      }

      // Generate phonetic transcription
      final phoneticTranscription = await _generatePhoneticTranscription(text, languageCode, dialect);
      final simplifiedPhonetics = await _generateSimplifiedPhonetics(text, languageCode);

      // Generate audio files
      final nativeAudioPath = await _generateNativeAudio(text, languageCode, dialect);
      final slowAudioPath = await _generateSlowAudio(text, languageCode, dialect);

      // Analyze syllable breakdown
      final syllableBreakdown = await _analyzeSyllableBreakdown(text, languageCode);

      // Analyze stress patterns
      final stressPatterns = await _analyzeStressPatterns(text, languageCode);

      // Get common mistakes for this language
      final commonMistakes = _getCommonMistakes(text, languageCode);

      // Generate practice exercises
      final exercises = await _generatePracticeExercises(text, languageCode, targetDifficulty);

      // Determine difficulty level
      final difficulty = targetDifficulty ?? _assessPronunciationDifficulty(text, languageCode);

      // Generate pronunciation tips
      final tips = await _generatePronunciationTips(text, languageCode, difficulty);

      // Find related words
      final relatedWords = await _findRelatedWords(text, languageCode);

      // Get regional variations
      final regionalVariations = await _getRegionalPronunciations(text, languageCode);

      // Calculate confidence score
      final confidenceScore = _calculatePronunciationConfidence(
        phoneticTranscription,
        syllableBreakdown,
        stressPatterns,
      );

      final guidance = EnhancedPronunciationGuidance(
        id: guidanceId,
        text: text,
        languageCode: languageCode,
        dialect: dialect,
        phoneticTranscription: phoneticTranscription,
        simplifiedPhonetics: simplifiedPhonetics,
        nativeAudioPath: nativeAudioPath,
        slowAudioPath: slowAudioPath,
        syllableBreakdown: syllableBreakdown,
        stressPatterns: stressPatterns,
        commonMistakes: commonMistakes,
        exercises: exercises,
        difficulty: difficulty,
        tips: tips,
        relatedWords: relatedWords,
        regionalVariations: regionalVariations,
        confidenceScore: confidenceScore,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Cache the guidance
      _guidanceCache[cacheKey] = guidance;

      // Notify listeners
      _guidanceController.add(guidance);

      // Track analytics
      await _analyticsService.trackEvent('pronunciation_guidance_generated', {
        'language': languageCode,
        'dialect': dialect,
        'difficulty': difficulty.name,
        'text_length': text.length,
        'confidence_score': confidenceScore,
      });

      return guidance;
    } catch (e) {
      _loggingService.error('Error generating pronunciation guidance: $e');
      rethrow;
    }
  }

  /// Record practice session and update progress
  Future<PronunciationProgress> recordPracticeSession({
    required String guidanceId,
    required double accuracyScore,
    required int attemptCount,
    required Duration practiceTime,
    List<String>? improvedSounds,
    List<String>? needsWorkSounds,
  }) async {
    try {
      final progress = PronunciationProgress(
        sessionId: const Uuid().v4(),
        practiceDate: DateTime.now(),
        accuracyScore: accuracyScore,
        attemptCount: attemptCount,
        practiceTime: practiceTime,
        improvedSounds: improvedSounds ?? [],
        needsWorkSounds: needsWorkSounds ?? [],
      );

      // Update guidance with practice history
      final guidance = _guidanceCache.values.firstWhere(
        (g) => g.id == guidanceId,
        orElse: () => throw Exception('Guidance not found'),
      );

      final updatedGuidance = guidance.copyWith(
        practiceHistory: [...guidance.practiceHistory, progress],
        updatedAt: DateTime.now(),
      );

      // Update cache
      final cacheKey = _guidanceCache.keys.firstWhere(
        (key) => _guidanceCache[key]?.id == guidanceId,
      );
      _guidanceCache[cacheKey] = updatedGuidance;

      // Save progress
      await _savePracticeProgress(progress);

      // Notify listeners
      _progressController.add(progress);
      _guidanceController.add(updatedGuidance);

      // Track analytics
      await _analyticsService.trackEvent('pronunciation_practice_completed', {
        'accuracy_score': accuracyScore,
        'attempt_count': attemptCount,
        'practice_time_seconds': practiceTime.inSeconds,
        'improved_sounds_count': improvedSounds?.length ?? 0,
        'needs_work_sounds_count': needsWorkSounds?.length ?? 0,
      });

      return progress;
    } catch (e) {
      _loggingService.error('Error recording practice session: $e');
      rethrow;
    }
  }

  /// Get pronunciation guidance by ID
  EnhancedPronunciationGuidance? getPronunciationGuidance(String id) {
    return _guidanceCache.values.where((guidance) => guidance.id == id).firstOrNull;
  }

  /// Get practice history for a user
  Future<List<PronunciationProgress>> getPracticeHistory({
    String? languageCode,
    DateTime? since,
    int? limit,
  }) async {
    try {
      final historyJson = _prefs.getString('pronunciation_practice_history');
      if (historyJson == null) return [];

      final List<dynamic> historyList = jsonDecode(historyJson);
      List<PronunciationProgress> history = historyList
          .map((json) => PronunciationProgress.fromJson(json as Map<String, dynamic>))
          .toList();

      // Apply filters
      if (since != null) {
        history = history.where((progress) => progress.practiceDate.isAfter(since)).toList();
      }

      // Sort by date (most recent first)
      history.sort((a, b) => b.practiceDate.compareTo(a.practiceDate));

      // Apply limit
      if (limit != null && history.length > limit) {
        history = history.take(limit).toList();
      }

      return history;
    } catch (e) {
      _loggingService.error('Error getting practice history: $e');
      return [];
    }
  }

  /// Generate phonetic transcription (IPA)
  Future<String?> _generatePhoneticTranscription(String text, String languageCode, String? dialect) async {
    // In a real implementation, this would use a phonetic transcription service
    // For now, we'll return a mock transcription
    await Future.delayed(const Duration(milliseconds: 200));
    
    final rules = _phoneticRules[languageCode];
    if (rules != null) {
      return rules.transcribeText(text);
    }
    
    return null;
  }

  /// Generate simplified phonetics for non-linguists
  Future<String?> _generateSimplifiedPhonetics(String text, String languageCode) async {
    // Simplified phonetic representation
    await Future.delayed(const Duration(milliseconds: 150));
    
    // Mock implementation - would use simplified phonetic rules
    return text.toLowerCase().replaceAll(RegExp(r'[^a-z\s]'), '');
  }

  /// Generate native speed audio
  Future<String?> _generateNativeAudio(String text, String languageCode, String? dialect) async {
    try {
      // In a real implementation, this would use TTS service
      await Future.delayed(const Duration(milliseconds: 500));
      
      final directory = await getTemporaryDirectory();
      final fileName = 'pronunciation_native_${const Uuid().v4()}.m4a';
      final filePath = '${directory.path}/$fileName';
      
      // Create mock audio file
      final file = File(filePath);
      await file.writeAsString('Mock native audio data for: $text in $languageCode');
      
      return filePath;
    } catch (e) {
      _loggingService.error('Error generating native audio: $e');
      return null;
    }
  }

  /// Generate slow speed audio
  Future<String?> _generateSlowAudio(String text, String languageCode, String? dialect) async {
    try {
      await Future.delayed(const Duration(milliseconds: 600));
      
      final directory = await getTemporaryDirectory();
      final fileName = 'pronunciation_slow_${const Uuid().v4()}.m4a';
      final filePath = '${directory.path}/$fileName';
      
      final file = File(filePath);
      await file.writeAsString('Mock slow audio data for: $text in $languageCode');
      
      return filePath;
    } catch (e) {
      _loggingService.error('Error generating slow audio: $e');
      return null;
    }
  }

  /// Analyze syllable breakdown
  Future<List<SyllableBreakdown>> _analyzeSyllableBreakdown(String text, String languageCode) async {
    final syllables = <SyllableBreakdown>[];
    
    // Mock syllable analysis - would use proper syllabification rules
    final words = text.split(' ');
    int position = 0;
    
    for (final word in words) {
      final wordSyllables = _splitIntoSyllables(word, languageCode);
      for (int i = 0; i < wordSyllables.length; i++) {
        syllables.add(SyllableBreakdown(
          syllable: wordSyllables[i],
          phonetic: '[${wordSyllables[i]}]', // Mock phonetic
          isPrimaryStress: i == 0, // Mock stress - first syllable
          position: position++,
        ));
      }
    }
    
    return syllables;
  }

  /// Analyze stress patterns
  Future<List<StressPattern>> _analyzeStressPatterns(String text, String languageCode) async {
    // Mock stress pattern analysis
    return [
      const StressPattern(
        pattern: '1-0',
        description: 'Primary stress on first syllable',
        stressedPositions: [0],
      ),
    ];
  }

  /// Get common pronunciation mistakes
  List<PronunciationMistake> _getCommonMistakes(String text, String languageCode) {
    return _commonMistakes[languageCode] ?? [];
  }

  /// Generate practice exercises
  Future<List<PronunciationExercise>> _generatePracticeExercises(
    String text,
    String languageCode,
    PronunciationDifficulty? difficulty,
  ) async {
    final exercises = <PronunciationExercise>[];
    
    // Generate minimal pairs exercise
    exercises.add(PronunciationExercise(
      id: const Uuid().v4(),
      type: ExerciseType.minimalPairs,
      instruction: 'Practice distinguishing between similar sounds',
      targetSound: 'th',
      practiceWords: ['think', 'sink', 'thank', 'sank'],
      difficulty: difficulty?.index ?? 2,
    ));
    
    return exercises;
  }

  /// Assess pronunciation difficulty
  PronunciationDifficulty _assessPronunciationDifficulty(String text, String languageCode) {
    // Mock difficulty assessment - would analyze phonetic complexity
    if (text.length < 5) return PronunciationDifficulty.beginner;
    if (text.length < 15) return PronunciationDifficulty.intermediate;
    return PronunciationDifficulty.advanced;
  }

  /// Generate pronunciation tips
  Future<List<PronunciationTip>> _generatePronunciationTips(
    String text,
    String languageCode,
    PronunciationDifficulty difficulty,
  ) async {
    return [
      const PronunciationTip(
        category: 'mouth_position',
        tip: 'Keep your tongue relaxed',
        explanation: 'Tension in the tongue can affect pronunciation clarity',
      ),
    ];
  }

  /// Find related words with similar pronunciation
  Future<List<RelatedWord>> _findRelatedWords(String text, String languageCode) async {
    // Mock related words - would use phonetic similarity matching
    return [
      const RelatedWord(
        word: 'similar',
        phonetic: '[ˈsɪmɪlər]',
        meaning: 'having resemblance',
        similarity: 0.8,
      ),
    ];
  }

  /// Get regional pronunciation variations
  Future<List<RegionalPronunciation>> _getRegionalPronunciations(String text, String languageCode) async {
    // Mock regional variations
    return [
      const RegionalPronunciation(
        region: 'US',
        accent: 'General American',
        phonetic: '[mock]',
        description: 'Standard American pronunciation',
      ),
    ];
  }

  /// Calculate pronunciation confidence score
  double _calculatePronunciationConfidence(
    String? phoneticTranscription,
    List<SyllableBreakdown> syllableBreakdown,
    List<StressPattern> stressPatterns,
  ) {
    double score = 0.3; // Base score
    
    if (phoneticTranscription != null) score += 0.3;
    if (syllableBreakdown.isNotEmpty) score += 0.2;
    if (stressPatterns.isNotEmpty) score += 0.2;
    
    return score.clamp(0.0, 1.0);
  }

  /// Split text into syllables (mock implementation)
  List<String> _splitIntoSyllables(String word, String languageCode) {
    // Mock syllabification - would use proper rules
    if (word.length <= 3) return [word];
    
    final mid = word.length ~/ 2;
    return [word.substring(0, mid), word.substring(mid)];
  }

  /// Save practice progress to storage
  Future<void> _savePracticeProgress(PronunciationProgress progress) async {
    try {
      final historyJson = _prefs.getString('pronunciation_practice_history');
      List<dynamic> history = historyJson != null ? jsonDecode(historyJson) : [];
      
      history.add(progress.toJson());
      
      // Keep only last 100 sessions
      if (history.length > 100) {
        history = history.sublist(history.length - 100);
      }
      
      await _prefs.setString('pronunciation_practice_history', jsonEncode(history));
    } catch (e) {
      _loggingService.error('Error saving practice progress: $e');
    }
  }

  /// Initialize default phonetic rules
  void _initializeDefaultPhoneticRules() {
    _phoneticRules = {
      'en': PhoneticRules(
        language: 'en',
        rules: {},
      ),
    };
  }

  /// Initialize default pronunciation mistakes
  void _initializeDefaultMistakes() {
    _commonMistakes = {
      'en': [
        const PronunciationMistake(
          incorrectPronunciation: 'sink',
          correctPronunciation: 'think',
          explanation: 'The "th" sound is often replaced with "s"',
          tip: 'Place your tongue between your teeth',
          commonFor: ['es', 'fr', 'de'],
        ),
      ],
    };
  }

  /// Dispose of resources
  void dispose() {
    _guidanceController.close();
    _progressController.close();
  }
}

/// Helper class for phonetic rules
class PhoneticRules {
  final String language;
  final Map<String, String> rules;

  const PhoneticRules({
    required this.language,
    required this.rules,
  });

  factory PhoneticRules.fromJson(Map<String, dynamic> json) {
    return PhoneticRules(
      language: json['language'] as String,
      rules: (json['rules'] as Map<String, dynamic>).cast<String, String>(),
    );
  }

  String transcribeText(String text) {
    // Mock transcription - would apply phonetic rules
    return '[${text.toLowerCase()}]';
  }
}

extension EnhancedPronunciationGuidanceListExtension on List<EnhancedPronunciationGuidance> {
  EnhancedPronunciationGuidance? get firstOrNull {
    return isEmpty ? null : first;
  }
}
