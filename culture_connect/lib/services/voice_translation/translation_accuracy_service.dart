import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/translation/translation_accuracy_feedback.dart';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/analytics_service.dart';

/// Service for managing translation accuracy feedback and improvement
class TranslationAccuracyService {
  final SharedPreferences _prefs;
  final LoggingService _loggingService;
  final AnalyticsService _analyticsService;

  /// List of feedback entries
  List<TranslationAccuracyFeedback> _feedbackEntries = [];

  /// Stream controller for feedback updates
  final StreamController<List<TranslationAccuracyFeedback>> _feedbackController =
      StreamController<List<TranslationAccuracyFeedback>>.broadcast();

  /// Stream controller for accuracy metrics
  final StreamController<AccuracyMetrics> _metricsController =
      StreamController<AccuracyMetrics>.broadcast();

  /// Current user ID
  String? _currentUserId;

  /// Cache for translation quality assessments
  final Map<String, TranslationQualityAssessment> _qualityCache = {};

  TranslationAccuracyService(
    this._prefs,
    this._loggingService,
    this._analyticsService,
  ) {
    _loadFeedbackEntries();
  }

  /// Stream of feedback entries
  Stream<List<TranslationAccuracyFeedback>> get feedbackStream =>
      _feedbackController.stream;

  /// Stream of accuracy metrics
  Stream<AccuracyMetrics> get metricsStream => _metricsController.stream;

  /// Get all feedback entries
  List<TranslationAccuracyFeedback> get feedbackEntries => _feedbackEntries;

  /// Set current user ID
  void setCurrentUserId(String userId) {
    _currentUserId = userId;
  }

  /// Load feedback entries from storage
  Future<void> _loadFeedbackEntries() async {
    try {
      final feedbackJson = _prefs.getString('translation_feedback_entries');
      if (feedbackJson != null) {
        final List<dynamic> feedbackList = jsonDecode(feedbackJson);
        _feedbackEntries = feedbackList
            .map((json) => TranslationAccuracyFeedback.fromJson(json as Map<String, dynamic>))
            .toList();
        
        _feedbackController.add(_feedbackEntries);
        _updateMetrics();
      }
    } catch (e) {
      _loggingService.warning('Error loading feedback entries: $e');
    }
  }

  /// Save feedback entries to storage
  Future<void> _saveFeedbackEntries() async {
    try {
      final feedbackJson = jsonEncode(_feedbackEntries.map((entry) => entry.toJson()).toList());
      await _prefs.setString('translation_feedback_entries', feedbackJson);
    } catch (e) {
      _loggingService.warning('Error saving feedback entries: $e');
    }
  }

  /// Submit feedback for a translation
  Future<TranslationAccuracyFeedback> submitFeedback({
    required String translationId,
    required String originalText,
    required String translatedText,
    required String sourceLanguage,
    required String targetLanguage,
    required TranslationAccuracyRating accuracyRating,
    required TranslationFeedbackType feedbackType,
    required double userConfidence,
    String? comments,
    String? suggestedCorrection,
    List<TranslationIssue>? issues,
    List<ImprovementSuggestion>? improvements,
    double? originalConfidence,
    bool isVerifiedTranslator = false,
    LanguageProficiencyLevel? userProficiency,
    TranslationContext? context,
  }) async {
    try {
      final feedback = TranslationAccuracyFeedback(
        id: const Uuid().v4(),
        translationId: translationId,
        userId: _currentUserId ?? 'anonymous',
        originalText: originalText,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        accuracyRating: accuracyRating,
        feedbackType: feedbackType,
        comments: comments,
        suggestedCorrection: suggestedCorrection,
        issues: issues ?? [],
        improvements: improvements ?? [],
        originalConfidence: originalConfidence,
        userConfidence: userConfidence,
        isVerifiedTranslator: isVerifiedTranslator,
        userProficiency: userProficiency,
        context: context,
        createdAt: DateTime.now(),
      );

      _feedbackEntries.add(feedback);
      await _saveFeedbackEntries();
      
      _feedbackController.add(_feedbackEntries);
      _updateMetrics();

      // Track analytics
      await _analyticsService.trackEvent('translation_feedback_submitted', {
        'accuracy_rating': accuracyRating.name,
        'feedback_type': feedbackType.name,
        'source_language': sourceLanguage,
        'target_language': targetLanguage,
        'has_correction': suggestedCorrection != null,
        'user_confidence': userConfidence,
      });

      _loggingService.info('Translation feedback submitted: ${feedback.id}');
      return feedback;
    } catch (e) {
      _loggingService.error('Error submitting feedback: $e');
      rethrow;
    }
  }

  /// Get feedback for a specific translation
  List<TranslationAccuracyFeedback> getFeedbackForTranslation(String translationId) {
    return _feedbackEntries.where((feedback) => feedback.translationId == translationId).toList();
  }

  /// Get feedback by language pair
  List<TranslationAccuracyFeedback> getFeedbackByLanguagePair(
    String sourceLanguage,
    String targetLanguage,
  ) {
    return _feedbackEntries.where((feedback) =>
        feedback.sourceLanguage == sourceLanguage &&
        feedback.targetLanguage == targetLanguage).toList();
  }

  /// Get feedback by accuracy rating
  List<TranslationAccuracyFeedback> getFeedbackByRating(TranslationAccuracyRating rating) {
    return _feedbackEntries.where((feedback) => feedback.accuracyRating == rating).toList();
  }

  /// Get recent feedback (last 30 days)
  List<TranslationAccuracyFeedback> getRecentFeedback() {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    return _feedbackEntries.where((feedback) => feedback.createdAt.isAfter(thirtyDaysAgo)).toList();
  }

  /// Get top issues across all feedback
  List<String> getTopIssues({int limit = 10}) {
    final issueCount = <String, int>{};
    
    for (final feedback in _feedbackEntries) {
      for (final issue in feedback.issues) {
        issueCount[issue.type] = (issueCount[issue.type] ?? 0) + 1;
      }
    }

    final sortedIssues = issueCount.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedIssues.take(limit).map((entry) => entry.key).toList();
  }

  /// Get improvement suggestions for a language pair
  List<ImprovementSuggestion> getImprovementSuggestions(
    String sourceLanguage,
    String targetLanguage,
  ) {
    final suggestions = <ImprovementSuggestion>[];
    
    final relevantFeedback = getFeedbackByLanguagePair(sourceLanguage, targetLanguage);
    for (final feedback in relevantFeedback) {
      suggestions.addAll(feedback.improvements);
    }

    // Sort by confidence and remove duplicates
    suggestions.sort((a, b) => b.confidence.compareTo(a.confidence));
    return suggestions.toSet().toList();
  }

  /// Assess translation quality based on historical feedback
  Future<TranslationQualityAssessment> assessTranslationQuality(
    VoiceTranslationModel translation,
  ) async {
    try {
      final cacheKey = '${translation.sourceLanguageCode}_${translation.targetLanguageCode}_${translation.originalText?.hashCode}';
      
      // Check cache first
      if (_qualityCache.containsKey(cacheKey)) {
        return _qualityCache[cacheKey]!;
      }

      // Get similar translations and their feedback
      final similarFeedback = _findSimilarTranslationFeedback(
        translation.originalText ?? '',
        translation.sourceLanguageCode,
        translation.targetLanguageCode,
      );

      double predictedAccuracy = 0.8; // Default baseline
      List<String> potentialIssues = [];
      List<ImprovementSuggestion> suggestions = [];

      if (similarFeedback.isNotEmpty) {
        // Calculate average accuracy from similar translations
        final avgAccuracy = similarFeedback.fold<double>(
          0.0,
          (sum, feedback) => sum + feedback.accuracyScore,
        ) / similarFeedback.length;
        
        predictedAccuracy = avgAccuracy;

        // Collect common issues
        final issueFrequency = <String, int>{};
        for (final feedback in similarFeedback) {
          for (final issue in feedback.issues) {
            issueFrequency[issue.type] = (issueFrequency[issue.type] ?? 0) + 1;
          }
        }

        potentialIssues = issueFrequency.entries
            .where((entry) => entry.value >= 2) // Issues that appear at least twice
            .map((entry) => entry.key)
            .toList();

        // Collect improvement suggestions
        for (final feedback in similarFeedback) {
          suggestions.addAll(feedback.improvements);
        }
      }

      final assessment = TranslationQualityAssessment(
        translationId: translation.id,
        predictedAccuracy: predictedAccuracy,
        confidenceLevel: similarFeedback.length >= 3 ? 0.8 : 0.5,
        potentialIssues: potentialIssues,
        suggestions: suggestions.take(5).toList(),
        basedOnSamples: similarFeedback.length,
        createdAt: DateTime.now(),
      );

      // Cache the assessment
      _qualityCache[cacheKey] = assessment;

      return assessment;
    } catch (e) {
      _loggingService.error('Error assessing translation quality: $e');
      
      // Return default assessment on error
      return TranslationQualityAssessment(
        translationId: translation.id,
        predictedAccuracy: 0.8,
        confidenceLevel: 0.3,
        potentialIssues: [],
        suggestions: [],
        basedOnSamples: 0,
        createdAt: DateTime.now(),
      );
    }
  }

  /// Find feedback for similar translations
  List<TranslationAccuracyFeedback> _findSimilarTranslationFeedback(
    String text,
    String sourceLanguage,
    String targetLanguage,
  ) {
    final words = text.toLowerCase().split(' ');
    final similarFeedback = <TranslationAccuracyFeedback>[];

    for (final feedback in _feedbackEntries) {
      if (feedback.sourceLanguage == sourceLanguage &&
          feedback.targetLanguage == targetLanguage) {
        
        final feedbackWords = feedback.originalText.toLowerCase().split(' ');
        final similarity = _calculateTextSimilarity(words, feedbackWords);
        
        if (similarity > 0.3) { // 30% similarity threshold
          similarFeedback.add(feedback);
        }
      }
    }

    return similarFeedback;
  }

  /// Calculate text similarity between two word lists
  double _calculateTextSimilarity(List<String> words1, List<String> words2) {
    if (words1.isEmpty || words2.isEmpty) return 0.0;

    final set1 = words1.toSet();
    final set2 = words2.toSet();
    final intersection = set1.intersection(set2);
    final union = set1.union(set2);

    return intersection.length / union.length;
  }

  /// Update accuracy metrics
  void _updateMetrics() {
    if (_feedbackEntries.isEmpty) return;

    final recentFeedback = getRecentFeedback();
    final totalFeedback = _feedbackEntries.length;
    
    // Calculate average accuracy
    final avgAccuracy = _feedbackEntries.fold<double>(
      0.0,
      (sum, feedback) => sum + feedback.accuracyScore,
    ) / totalFeedback;

    // Calculate accuracy by rating
    final ratingCounts = <TranslationAccuracyRating, int>{};
    for (final feedback in recentFeedback) {
      ratingCounts[feedback.accuracyRating] = (ratingCounts[feedback.accuracyRating] ?? 0) + 1;
    }

    // Calculate improvement trend (comparing last 15 days vs previous 15 days)
    final now = DateTime.now();
    final last15Days = recentFeedback.where((f) => 
        f.createdAt.isAfter(now.subtract(const Duration(days: 15)))).toList();
    final previous15Days = recentFeedback.where((f) => 
        f.createdAt.isBefore(now.subtract(const Duration(days: 15))) &&
        f.createdAt.isAfter(now.subtract(const Duration(days: 30)))).toList();

    double improvementTrend = 0.0;
    if (last15Days.isNotEmpty && previous15Days.isNotEmpty) {
      final recentAvg = last15Days.fold<double>(0.0, (sum, f) => sum + f.accuracyScore) / last15Days.length;
      final previousAvg = previous15Days.fold<double>(0.0, (sum, f) => sum + f.accuracyScore) / previous15Days.length;
      improvementTrend = recentAvg - previousAvg;
    }

    final metrics = AccuracyMetrics(
      totalFeedbackCount: totalFeedback,
      recentFeedbackCount: recentFeedback.length,
      averageAccuracy: avgAccuracy,
      ratingDistribution: ratingCounts,
      topIssues: getTopIssues(limit: 5),
      improvementTrend: improvementTrend,
      lastUpdated: DateTime.now(),
    );

    _metricsController.add(metrics);
  }

  /// Vote on feedback helpfulness
  Future<void> voteOnFeedback(String feedbackId, bool isHelpful) async {
    try {
      final index = _feedbackEntries.indexWhere((feedback) => feedback.id == feedbackId);
      if (index >= 0) {
        final feedback = _feedbackEntries[index];
        final updatedFeedback = feedback.copyWith(
          helpfulnessVotes: feedback.helpfulnessVotes + (isHelpful ? 1 : -1),
        );
        
        _feedbackEntries[index] = updatedFeedback;
        await _saveFeedbackEntries();
        _feedbackController.add(_feedbackEntries);
      }
    } catch (e) {
      _loggingService.error('Error voting on feedback: $e');
    }
  }

  /// Clear old feedback entries (older than 6 months)
  Future<void> cleanupOldFeedback() async {
    try {
      final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));
      final initialCount = _feedbackEntries.length;
      
      _feedbackEntries.removeWhere((feedback) => feedback.createdAt.isBefore(sixMonthsAgo));
      
      if (_feedbackEntries.length != initialCount) {
        await _saveFeedbackEntries();
        _feedbackController.add(_feedbackEntries);
        _updateMetrics();
        
        _loggingService.info('Cleaned up ${initialCount - _feedbackEntries.length} old feedback entries');
      }
    } catch (e) {
      _loggingService.error('Error cleaning up old feedback: $e');
    }
  }

  /// Dispose of resources
  void dispose() {
    _feedbackController.close();
    _metricsController.close();
  }
}

/// Model for translation quality assessment
class TranslationQualityAssessment {
  final String translationId;
  final double predictedAccuracy;
  final double confidenceLevel;
  final List<String> potentialIssues;
  final List<ImprovementSuggestion> suggestions;
  final int basedOnSamples;
  final DateTime createdAt;

  const TranslationQualityAssessment({
    required this.translationId,
    required this.predictedAccuracy,
    required this.confidenceLevel,
    required this.potentialIssues,
    required this.suggestions,
    required this.basedOnSamples,
    required this.createdAt,
  });
}

/// Model for accuracy metrics
class AccuracyMetrics {
  final int totalFeedbackCount;
  final int recentFeedbackCount;
  final double averageAccuracy;
  final Map<TranslationAccuracyRating, int> ratingDistribution;
  final List<String> topIssues;
  final double improvementTrend;
  final DateTime lastUpdated;

  const AccuracyMetrics({
    required this.totalFeedbackCount,
    required this.recentFeedbackCount,
    required this.averageAccuracy,
    required this.ratingDistribution,
    required this.topIssues,
    required this.improvementTrend,
    required this.lastUpdated,
  });
}
