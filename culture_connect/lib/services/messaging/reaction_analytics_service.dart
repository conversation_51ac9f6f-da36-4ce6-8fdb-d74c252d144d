import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for tracking and analyzing message reaction patterns
class ReactionAnalyticsService {
  static final ReactionAnalyticsService _instance = ReactionAnalyticsService._internal();
  factory ReactionAnalyticsService() => _instance;
  ReactionAnalyticsService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LoggingService _loggingService = LoggingService();

  /// Track a reaction event
  Future<void> trackReactionEvent({
    required String userId,
    required String messageId,
    required String chatId,
    required String emoji,
    required String reactionType,
    required ReactionEventType eventType,
  }) async {
    try {
      final eventData = {
        'userId': userId,
        'messageId': messageId,
        'chatId': chatId,
        'emoji': emoji,
        'reactionType': reactionType,
        'eventType': eventType.toString().split('.').last,
        'timestamp': FieldValue.serverTimestamp(),
        'platform': 'mobile',
      };

      await _firestore
          .collection('reaction_analytics')
          .add(eventData);

      // Update user reaction statistics
      await _updateUserReactionStats(userId, emoji, eventType);
      
      // Update chat reaction statistics
      await _updateChatReactionStats(chatId, emoji, eventType);

    } catch (e) {
      _loggingService.warning('Failed to track reaction event: $e');
    }
  }

  /// Get popular reactions for a user
  Future<Map<String, int>> getUserPopularReactions(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('user_reaction_stats')
          .doc(userId)
          .get();

      if (!snapshot.exists) {
        return {};
      }

      final data = snapshot.data() as Map<String, dynamic>;
      final reactions = data['reactions'] as Map<String, dynamic>? ?? {};
      
      return reactions.map((key, value) => MapEntry(key, value as int));
    } catch (e) {
      _loggingService.warning('Failed to get user popular reactions: $e');
      return {};
    }
  }

  /// Get reaction trends for a chat
  Future<Map<String, dynamic>> getChatReactionTrends(String chatId) async {
    try {
      final snapshot = await _firestore
          .collection('chat_reaction_stats')
          .doc(chatId)
          .get();

      if (!snapshot.exists) {
        return {};
      }

      return snapshot.data() as Map<String, dynamic>;
    } catch (e) {
      _loggingService.warning('Failed to get chat reaction trends: $e');
      return {};
    }
  }

  /// Get reaction analytics for a specific time period
  Future<List<Map<String, dynamic>>> getReactionAnalytics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final snapshot = await _firestore
          .collection('reaction_analytics')
          .where('userId', isEqualTo: userId)
          .where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('timestamp', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      _loggingService.warning('Failed to get reaction analytics: $e');
      return [];
    }
  }

  /// Get most reacted messages in a chat
  Future<List<Map<String, dynamic>>> getMostReactedMessages(
    String chatId, {
    int limit = 10,
  }) async {
    try {
      final snapshot = await _firestore
          .collection('messages')
          .where('chatId', isEqualTo: chatId)
          .where('reactions', isNotEqualTo: null)
          .get();

      final messages = snapshot.docs.map((doc) {
        final data = doc.data();
        final reactions = data['reactions'] as Map<String, dynamic>? ?? {};
        final reactionCount = reactions.length;
        
        return {
          'messageId': doc.id,
          'content': data['content'] ?? '',
          'senderName': data['senderName'] ?? '',
          'timestamp': data['timestamp'],
          'reactionCount': reactionCount,
          'reactions': reactions,
        };
      }).toList();

      // Sort by reaction count
      messages.sort((a, b) => (b['reactionCount'] as int).compareTo(a['reactionCount'] as int));
      
      return messages.take(limit).toList();
    } catch (e) {
      _loggingService.warning('Failed to get most reacted messages: $e');
      return [];
    }
  }

  /// Update user reaction statistics
  Future<void> _updateUserReactionStats(
    String userId,
    String emoji,
    ReactionEventType eventType,
  ) async {
    try {
      final docRef = _firestore.collection('user_reaction_stats').doc(userId);
      
      if (eventType == ReactionEventType.added) {
        await docRef.set({
          'reactions.$emoji': FieldValue.increment(1),
          'totalReactions': FieldValue.increment(1),
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      } else if (eventType == ReactionEventType.removed) {
        await docRef.set({
          'reactions.$emoji': FieldValue.increment(-1),
          'totalReactions': FieldValue.increment(-1),
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      }
    } catch (e) {
      _loggingService.warning('Failed to update user reaction stats: $e');
    }
  }

  /// Update chat reaction statistics
  Future<void> _updateChatReactionStats(
    String chatId,
    String emoji,
    ReactionEventType eventType,
  ) async {
    try {
      final docRef = _firestore.collection('chat_reaction_stats').doc(chatId);
      
      if (eventType == ReactionEventType.added) {
        await docRef.set({
          'reactions.$emoji': FieldValue.increment(1),
          'totalReactions': FieldValue.increment(1),
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      } else if (eventType == ReactionEventType.removed) {
        await docRef.set({
          'reactions.$emoji': FieldValue.increment(-1),
          'totalReactions': FieldValue.increment(-1),
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      }
    } catch (e) {
      _loggingService.warning('Failed to update chat reaction stats: $e');
    }
  }

  /// Get reaction suggestions based on user history
  Future<List<String>> getReactionSuggestions(String userId) async {
    try {
      final userStats = await getUserPopularReactions(userId);
      
      // Sort by usage count and return top suggestions
      final sortedReactions = userStats.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      
      return sortedReactions
          .take(6)
          .map((entry) => entry.key)
          .toList();
    } catch (e) {
      _loggingService.warning('Failed to get reaction suggestions: $e');
      return ['👍', '❤️', '😂', '😮', '😢', '😡']; // Default suggestions
    }
  }

  /// Get reaction insights for a user
  Future<Map<String, dynamic>> getUserReactionInsights(String userId) async {
    try {
      final now = DateTime.now();
      final lastWeek = now.subtract(const Duration(days: 7));
      final lastMonth = now.subtract(const Duration(days: 30));

      final weeklyAnalytics = await getReactionAnalytics(
        userId: userId,
        startDate: lastWeek,
        endDate: now,
      );

      final monthlyAnalytics = await getReactionAnalytics(
        userId: userId,
        startDate: lastMonth,
        endDate: now,
      );

      final userStats = await getUserPopularReactions(userId);

      return {
        'weeklyReactionCount': weeklyAnalytics.length,
        'monthlyReactionCount': monthlyAnalytics.length,
        'favoriteReactions': userStats,
        'reactionTrend': _calculateReactionTrend(weeklyAnalytics, monthlyAnalytics),
        'mostActiveDay': _getMostActiveDay(weeklyAnalytics),
      };
    } catch (e) {
      _loggingService.warning('Failed to get user reaction insights: $e');
      return {};
    }
  }

  /// Calculate reaction trend (increasing/decreasing)
  String _calculateReactionTrend(
    List<Map<String, dynamic>> weeklyData,
    List<Map<String, dynamic>> monthlyData,
  ) {
    if (monthlyData.isEmpty) return 'stable';
    
    final weeklyCount = weeklyData.length;
    final monthlyAverage = monthlyData.length / 4; // 4 weeks in a month
    
    if (weeklyCount > monthlyAverage * 1.2) {
      return 'increasing';
    } else if (weeklyCount < monthlyAverage * 0.8) {
      return 'decreasing';
    } else {
      return 'stable';
    }
  }

  /// Get most active day for reactions
  String _getMostActiveDay(List<Map<String, dynamic>> analytics) {
    if (analytics.isEmpty) return 'No data';
    
    final dayCount = <String, int>{};
    
    for (final event in analytics) {
      final timestamp = event['timestamp'] as Timestamp?;
      if (timestamp != null) {
        final date = timestamp.toDate();
        final dayName = _getDayName(date.weekday);
        dayCount[dayName] = (dayCount[dayName] ?? 0) + 1;
      }
    }
    
    if (dayCount.isEmpty) return 'No data';
    
    final mostActiveEntry = dayCount.entries
        .reduce((a, b) => a.value > b.value ? a : b);
    
    return mostActiveEntry.key;
  }

  String _getDayName(int weekday) {
    const days = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday',
      'Friday', 'Saturday', 'Sunday'
    ];
    return days[weekday - 1];
  }
}

/// Types of reaction events for analytics
enum ReactionEventType {
  added,
  removed,
  changed,
}
