import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/messaging/message_schedule_model.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/offline_message_service.dart';

/// Service for managing scheduled messages
class MessageScheduleService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LoggingService _loggingService = LoggingService();
  final OfflineMessageService _messageService = OfflineMessageService();
  
  bool _isInitialized = false;
  Timer? _schedulerTimer;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _loggingService.info('MessageScheduleService', 'Initializing service');
      await _messageService.initialize();
      _startScheduler();
      _isInitialized = true;
    } catch (e) {
      _loggingService.error('MessageScheduleService', 'Failed to initialize: $e');
      rethrow;
    }
  }

  /// Start the message scheduler
  void _startScheduler() {
    _schedulerTimer?.cancel();
    _schedulerTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _processPendingMessages();
    });
  }

  /// Stop the message scheduler
  void stopScheduler() {
    _schedulerTimer?.cancel();
    _schedulerTimer = null;
  }

  /// Schedule a message
  Future<MessageScheduleModel?> scheduleMessage({
    required String chatId,
    required String senderId,
    required String recipientId,
    required String text,
    required MessageType type,
    required DateTime scheduledFor,
    String? mediaUrl,
    Map<String, dynamic>? metadata,
  }) async {
    await initialize();
    
    try {
      // Validate scheduled time
      if (scheduledFor.isBefore(DateTime.now())) {
        throw Exception('Cannot schedule message in the past');
      }

      final scheduleId = DateTime.now().millisecondsSinceEpoch.toString();
      final now = DateTime.now();
      
      final scheduledMessage = MessageScheduleModel(
        id: scheduleId,
        chatId: chatId,
        senderId: senderId,
        recipientId: recipientId,
        text: text,
        type: type,
        mediaUrl: mediaUrl,
        scheduledFor: scheduledFor,
        createdAt: now,
        status: MessageScheduleStatus.pending,
        metadata: metadata,
      );

      await _firestore
          .collection('scheduled_messages')
          .doc(scheduleId)
          .set(scheduledMessage.toJson());

      _loggingService.info(
        'MessageScheduleService',
        'Message scheduled successfully',
        {
          'scheduleId': scheduleId,
          'scheduledFor': scheduledFor.toIso8601String(),
        },
      );

      return scheduledMessage;
    } catch (e) {
      _loggingService.error(
        'MessageScheduleService',
        'Failed to schedule message',
        {'error': e.toString()},
      );
      return null;
    }
  }

  /// Cancel a scheduled message
  Future<bool> cancelScheduledMessage(String scheduleId) async {
    await initialize();
    
    try {
      final scheduleDoc = await _firestore
          .collection('scheduled_messages')
          .doc(scheduleId)
          .get();

      if (!scheduleDoc.exists) {
        throw Exception('Scheduled message not found');
      }

      final scheduledMessage = MessageScheduleModel.fromJson({
        ...scheduleDoc.data()!,
        'id': scheduleDoc.id,
      });

      if (!scheduledMessage.canCancel) {
        throw Exception('Cannot cancel this scheduled message');
      }

      await _firestore
          .collection('scheduled_messages')
          .doc(scheduleId)
          .update({
        'status': MessageScheduleStatus.cancelled.toString().split('.').last,
      });

      _loggingService.info(
        'MessageScheduleService',
        'Scheduled message cancelled',
        {'scheduleId': scheduleId},
      );

      return true;
    } catch (e) {
      _loggingService.error(
        'MessageScheduleService',
        'Failed to cancel scheduled message',
        {'scheduleId': scheduleId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// Reschedule a message
  Future<bool> rescheduleMessage({
    required String scheduleId,
    required DateTime newScheduledTime,
  }) async {
    await initialize();
    
    try {
      final scheduleDoc = await _firestore
          .collection('scheduled_messages')
          .doc(scheduleId)
          .get();

      if (!scheduleDoc.exists) {
        throw Exception('Scheduled message not found');
      }

      final scheduledMessage = MessageScheduleModel.fromJson({
        ...scheduleDoc.data()!,
        'id': scheduleDoc.id,
      });

      if (!scheduledMessage.canReschedule) {
        throw Exception('Cannot reschedule this message');
      }

      if (newScheduledTime.isBefore(DateTime.now())) {
        throw Exception('Cannot reschedule message to the past');
      }

      await _firestore
          .collection('scheduled_messages')
          .doc(scheduleId)
          .update({
        'scheduledFor': Timestamp.fromDate(newScheduledTime),
        'status': MessageScheduleStatus.pending.toString().split('.').last,
        'retryCount': 0,
        'failureReason': FieldValue.delete(),
      });

      _loggingService.info(
        'MessageScheduleService',
        'Message rescheduled successfully',
        {
          'scheduleId': scheduleId,
          'newScheduledTime': newScheduledTime.toIso8601String(),
        },
      );

      return true;
    } catch (e) {
      _loggingService.error(
        'MessageScheduleService',
        'Failed to reschedule message',
        {'scheduleId': scheduleId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// Get scheduled messages for a user
  Future<List<MessageScheduleModel>> getUserScheduledMessages(String userId) async {
    await initialize();
    
    try {
      final querySnapshot = await _firestore
          .collection('scheduled_messages')
          .where('senderId', isEqualTo: userId)
          .where('status', whereIn: [
            MessageScheduleStatus.pending.toString().split('.').last,
            MessageScheduleStatus.failed.toString().split('.').last,
          ])
          .orderBy('scheduledFor', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => MessageScheduleModel.fromJson({
                ...doc.data(),
                'id': doc.id,
              }))
          .toList();
    } catch (e) {
      _loggingService.error(
        'MessageScheduleService',
        'Failed to get user scheduled messages',
        {'userId': userId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// Process pending scheduled messages
  Future<void> _processPendingMessages() async {
    if (!_isInitialized) return;
    
    try {
      final now = DateTime.now();
      final querySnapshot = await _firestore
          .collection('scheduled_messages')
          .where('status', isEqualTo: MessageScheduleStatus.pending.toString().split('.').last)
          .where('scheduledFor', isLessThanOrEqualTo: Timestamp.fromDate(now))
          .limit(50) // Process in batches
          .get();

      for (final doc in querySnapshot.docs) {
        final scheduledMessage = MessageScheduleModel.fromJson({
          ...doc.data(),
          'id': doc.id,
        });

        await _sendScheduledMessage(scheduledMessage);
      }
    } catch (e) {
      _loggingService.error(
        'MessageScheduleService',
        'Failed to process pending messages',
        {'error': e.toString()},
      );
    }
  }

  /// Send a scheduled message
  Future<void> _sendScheduledMessage(MessageScheduleModel scheduledMessage) async {
    try {
      // Update status to sending
      await _firestore
          .collection('scheduled_messages')
          .doc(scheduledMessage.id)
          .update({
        'status': MessageScheduleStatus.sending.toString().split('.').last,
      });

      // Convert to regular message and send
      final message = scheduledMessage.toMessageModel();
      await _messageService.sendMessage(message);

      // Update status to sent
      await _firestore
          .collection('scheduled_messages')
          .doc(scheduledMessage.id)
          .update({
        'status': MessageScheduleStatus.sent.toString().split('.').last,
        'sentAt': Timestamp.fromDate(DateTime.now()),
        'sentMessageId': message.id,
      });

      _loggingService.info(
        'MessageScheduleService',
        'Scheduled message sent successfully',
        {'scheduleId': scheduledMessage.id, 'messageId': message.id},
      );
    } catch (e) {
      // Update status to failed
      await _firestore
          .collection('scheduled_messages')
          .doc(scheduledMessage.id)
          .update({
        'status': MessageScheduleStatus.failed.toString().split('.').last,
        'failureReason': e.toString(),
        'retryCount': FieldValue.increment(1),
      });

      _loggingService.error(
        'MessageScheduleService',
        'Failed to send scheduled message',
        {'scheduleId': scheduledMessage.id, 'error': e.toString()},
      );
    }
  }

  /// Clean up old scheduled messages
  Future<void> cleanupOldScheduledMessages({int daysOld = 30}) async {
    await initialize();
    
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
      
      final oldMessages = await _firestore
          .collection('scheduled_messages')
          .where('createdAt', isLessThan: Timestamp.fromDate(cutoffDate))
          .where('status', whereIn: [
            MessageScheduleStatus.sent.toString().split('.').last,
            MessageScheduleStatus.cancelled.toString().split('.').last,
            MessageScheduleStatus.expired.toString().split('.').last,
          ])
          .get();

      final batch = _firestore.batch();
      
      for (final doc in oldMessages.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();

      _loggingService.info(
        'MessageScheduleService',
        'Cleaned up old scheduled messages',
        {'count': oldMessages.docs.length, 'daysOld': daysOld},
      );
    } catch (e) {
      _loggingService.error(
        'MessageScheduleService',
        'Failed to cleanup old scheduled messages',
        {'error': e.toString()},
      );
    }
  }

  /// Dispose resources
  void dispose() {
    stopScheduler();
  }
}
